﻿using MEP.PipeInsulation.Database;
using MEP.PipeInsulation.ViewModels;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace MEP.PipeInsulation.UI.Views.DataSources
{
    /// <summary>
    /// Interaction logic for PI_DataSourceViewNonCard.xaml
    /// </summary>
    public partial class PI_DataSourceViewNonCard : Page
    {
        private PI_MainViewModel _viewModel;

        public PI_DataSourceViewNonCard(PI_MainViewModel viewModel)
        {
            InitializeComponent();

            _viewModel = viewModel;
            _viewModel.ViewTable = PI_TableHelper.GetViewTable(viewModel, viewModel.SelectedDataSource, viewModel.SelectedDesignLevel);

            DataContext = viewModel;

            if (viewModel.SelectedDataSource != PI_SourceNames.Custom)
            {
                btn_SaveCustom.IsEnabled = false;
                dgv_Lookup.IsReadOnly = true; // Make the DataGrid read-only for non-custom sources
            }

        }

        private void btn_back_Click(object sender, RoutedEventArgs e)
        {
            NavigationService.GoBack();
        }

        private void btn_SaveCustom_Click(object sender, RoutedEventArgs e)
        {
            DataView dv = (DataView)dgv_Lookup.ItemsSource;
            DataTable editedViewTable = dv.ToTable();

            // Update custom lookup without BUID for combined lookup
            _viewModel.Data.PI_LookupCustom = PI_TableHelper.ConvertBackToOriginal(editedViewTable);

            // Create table for sql
            UpdateCustomTableInSql(PI_TableHelper.ConvertBackToOriginal(editedViewTable, true, _viewModel.Data.BUID));

            // Rebuild combined lookup
            _viewModel.Data.PI_LookupCombined = _viewModel.Data.CombineDataTables(_viewModel.Data.PI_LookupMaster, _viewModel.Data.PI_LookupCustom);

            NavigationService.GoBack();
        }

        private void UpdateCustomTableInSql(DataTable customDataTable)
        {
            var db = new PI_DatabaseClient();
            try
            {
                db.Connect();
                db.UpdateCustomTable(_viewModel.Data.BUID, customDataTable);
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show($"Failed to retrieve rows: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                db.Close();
            }
        }
    }
}
