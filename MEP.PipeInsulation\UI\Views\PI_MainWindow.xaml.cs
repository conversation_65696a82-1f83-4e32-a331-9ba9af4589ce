﻿using Autodesk.Revit.DB;
using MEP.PipeInsulation.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace MEP.PipeInsulation.UI.Views
{
    /// <summary>
    /// Interaction logic for PI_MainWindow.xaml
    /// </summary>
    public partial class PI_MainWindow : Window
    {
        PI_MainViewModel _viewModel;

        public PI_MainWindow(PI_MainViewModel viewModel)
        {
            InitializeComponent();

            _viewModel = viewModel;
            DataContext = _viewModel;

#if TargetYear2023 || TargetYear2024 || TargetYear2025
            MainFrame.Navigate(new PI_StartPage(_viewModel));
#else
            MainFrame.Navigate(new PI_StartPageNonCard(_viewModel));
#endif
        }

        private void OpenUrlButton_Click(object sender, RoutedEventArgs e)
        {

        }
    }
}
