﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PipeInsulation.CoreLogic
{
    public class PI_Database
    {
        public string CountryReference { get; set; }
        public string PipeType { get; set; }
        public string SystemSize { get; set; }
        public double MinDiameter { get; set; }
        public double MaxDiameter { get; set; }
        public double InteriorElastometric { get; set; }
        public double InteriorPolyethelene { get; set; }
        public double InteriorPhenolic { get; set; }
        public double ExteriorElastometric { get; set; }
        public double ExteriorPolyethelene { get; set; }
        public double ExteriorPhenolic { get; set; }

    }

}
