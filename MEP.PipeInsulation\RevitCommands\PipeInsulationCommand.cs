﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using BecaCommand;
using MEP.PipeInsulation.CoreLogic.FindExteriorWalls;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GEN.LinkedFileMapper.CoreLogic;
using Autodesk.Revit.DB.Plumbing;
using MEP.PipeInsulation.CoreLogic;
using MEP.PipeInsulation.UI.ModelessRevitForm;
using MEP.PipeInsulation.Models;
using MEP.PipeInsulation.ViewModels;
using MEP.PipeInsulation.UI.Views.ViewHandlers;
using MEP.PipeInsulation.CoreLogic.LogicEnhanced;
using Common.UI.Forms;
using System.Net;

namespace MEP.PipeInsulation.RevitCommands
{
    [Transaction(TransactionMode.Manual)]
    internal class PipeInsulationCommand : BecaBaseCommand
    {
        public override Result ExecuteBecaCommand(ExternalCommandData commandData, ref string message, Autodesk.Revit.DB.ElementSet elements)
        {
            UIApplication uiapp = commandData.Application;
            UIDocument uidoc = uiapp.ActiveUIDocument;
            var app = uiapp.Application;
            Document doc = uidoc.Document;

            _taskLogger.PreTaskStart();

            #region Core Logic
            var view3D = PipeInsulationHelper.InitializePI3DView(uiapp);

            // Check Parameters
            if (!new PI_ParameterChecker().ParametersIsAllGood(doc, doc.Application, PI_DefaultSettings.SharedParameterPath + "\\Beca_75_HVAC_Shared_Parameters.txt"))
                return Result.Failed;

            try
            {
                PI_MainWindowHandler.ShowForm(uiapp, _taskLogger, new PI_Data(doc, view3D));

                //ModelessPipeInsulationHandler.ShowForm(uiapp, _taskLogger, PipeInsulationHelper.InitializePI3DView(uiapp));
            }
            catch (Exception e)
            {
                MessageBox.Show(e.Message);
            }
            
            #endregion

            _taskLogger.PostTaskEnd("Summary.");
            return Result.Succeeded;
        }

        public override string GetAddinAuthor()
        {
            return "Firza Utama, Tristan Balme";
        }

        public override string GetAddinName()
        {
            return AddinNames.PipeInsulation.Value;
        }

        public override string GetCommandSubName()
        {
            return string.Empty;
        }
    }
}
