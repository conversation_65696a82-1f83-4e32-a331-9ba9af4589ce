﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PipeInsulation.CoreLogic.InteriorExteriorDetection
{
    public static class MessageBoxHelper
    {
        /// <summary>
        /// Shows a warning when no linked files are found.
        /// </summary>
        public static bool PromptNoLinkedFiles()
        {
            string message = "This model doesn't have any loaded linked file(s).\n" +
                             "The tool will attempt to detect interior elements by analyzing local walls, ceilings, and floors.\n" +
                             "Please ensure that these elements are present in the local document.\n\n" +
                             "Would you like to proceed?";
            string title = "Warning - No Linked Files Found";
            DialogResult result = MessageBox.Show(message, title, MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
            return result == DialogResult.Yes;
        }

        /// <summary>
        /// Warns the user about processing large numbers of elements.
        /// </summary>
        public static bool PromptLargeElementCount(int nCount)
        {
            string message = $"{nCount} elements will be processed.\n\n" +
                             "Processing 250 elements typically takes about 50 seconds if no linked elements are present.\n" +
                             "It is recommended to filter the elements to improve performance.\n\n" +
                             "Would you like to proceed?";
            string title = "Warning - Large Element Count";
            DialogResult result = MessageBox.Show(message, title, MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
            return result == DialogResult.Yes;
        }

        /// <summary>
        /// Displays a message when no interior elements are identified
        /// </summary>
        public static void ShowNoInteriorElementsMessage()
        {
            string message = "None of the elements were identified as interior. Are you sure this is acceptable?\n\n" +
                             "If you are unsure, please review your model and ensure that the elements are correctly set.\n" +
                             "You can click 'Reset All Interior' to reset all elements as interior.";
            string title = "No Interior Elements Found";

            MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
