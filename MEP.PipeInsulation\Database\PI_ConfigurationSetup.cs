﻿using System;
using System.Windows.Forms;
using Microsoft.Data.SqlClient;

namespace MEP.PipeInsulation.Database
{
    /// <summary>
    /// Utility class for setting up encrypted database configuration
    /// </summary>
    public static class PI_ConfigurationSetup
    {
        /// <summary>
        /// Shows a configuration dialog to set up database connection
        /// </summary>
        public static void ShowConfigurationDialog()
        {
            using (var form = new ConfigurationForm())
            {
                if (form.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        PI_SecureConfigManager.CreateEncryptedConfig(form.ConnectionString);
                        MessageBox.Show("Configuration saved successfully!", "Success",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Failed to save configuration: {ex.Message}", "Error",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        /// <summary>
        /// Creates encrypted configuration with provided connection details
        /// </summary>
        public static bool CreateConfiguration(string server, string database, string username, string password)
        {
            try
            {
                var builder = new SqlConnectionStringBuilder
                {
                    DataSource = server,
                    InitialCatalog = database,
                    UserID = username,
                    Password = password,
                    Encrypt = true,
                    TrustServerCertificate = false
                };

                PI_SecureConfigManager.CreateEncryptedConfig(builder.ConnectionString);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Creates configuration from a complete connection string
        /// </summary>
        public static bool CreateConfigurationFromConnectionString(string connectionString)
        {
            try
            {
                // Validate the connection string by attempting to parse it
                var builder = new SqlConnectionStringBuilder(connectionString);
                PI_SecureConfigManager.CreateEncryptedConfig(connectionString);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Checks if configuration exists and is valid
        /// </summary>
        public static bool IsConfigurationValid()
        {
            return PI_SecureConfigManager.ValidateConfiguration();
        }
    }

    /// <summary>
    /// Simple form for database configuration input
    /// </summary>
    internal class ConfigurationForm : Form
    {
        private TextBox txtServer;
        private TextBox txtDatabase;
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnOK;
        private Button btnCancel;
        private Button btnTest;

        public string ConnectionString { get; private set; }

        public ConfigurationForm()
        {
            InitializeComponent();
            LoadCurrentConfiguration();
        }

        private void InitializeComponent()
        {
            this.Text = "Database Configuration";
            this.Size = new System.Drawing.Size(400, 250);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Server
            var lblServer = new Label { Text = "Server:", Location = new System.Drawing.Point(12, 15), Size = new System.Drawing.Size(80, 23) };
            txtServer = new TextBox { Location = new System.Drawing.Point(100, 12), Size = new System.Drawing.Size(270, 23) };

            // Database
            var lblDatabase = new Label { Text = "Database:", Location = new System.Drawing.Point(12, 45), Size = new System.Drawing.Size(80, 23) };
            txtDatabase = new TextBox { Location = new System.Drawing.Point(100, 42), Size = new System.Drawing.Size(270, 23) };

            // Username
            var lblUsername = new Label { Text = "Username:", Location = new System.Drawing.Point(12, 75), Size = new System.Drawing.Size(80, 23) };
            txtUsername = new TextBox { Location = new System.Drawing.Point(100, 72), Size = new System.Drawing.Size(270, 23) };

            // Password
            var lblPassword = new Label { Text = "Password:", Location = new System.Drawing.Point(12, 105), Size = new System.Drawing.Size(80, 23) };
            txtPassword = new TextBox { Location = new System.Drawing.Point(100, 102), Size = new System.Drawing.Size(270, 23), UseSystemPasswordChar = true };

            // Buttons
            btnTest = new Button { Text = "Test Connection", Location = new System.Drawing.Point(12, 140), Size = new System.Drawing.Size(120, 30) };
            btnOK = new Button { Text = "OK", Location = new System.Drawing.Point(215, 180), Size = new System.Drawing.Size(75, 30), DialogResult = DialogResult.OK };
            btnCancel = new Button { Text = "Cancel", Location = new System.Drawing.Point(295, 180), Size = new System.Drawing.Size(75, 30), DialogResult = DialogResult.Cancel };

            btnTest.Click += BtnTest_Click;
            btnOK.Click += BtnOK_Click;

            this.Controls.AddRange(new Control[] {
                lblServer, txtServer, lblDatabase, txtDatabase,
                lblUsername, txtUsername, lblPassword, txtPassword,
                btnTest, btnOK, btnCancel
            });

            this.AcceptButton = btnOK;
            this.CancelButton = btnCancel;
        }

        private void LoadCurrentConfiguration()
        {
            try
            {
                if (PI_SecureConfigManager.ValidateConfiguration())
                {
                    string connectionString = PI_SecureConfigManager.GetConnectionString();
                    var builder = new SqlConnectionStringBuilder(connectionString);

                    txtServer.Text = builder.DataSource;
                    txtDatabase.Text = builder.InitialCatalog;
                    txtUsername.Text = builder.UserID;
                    txtPassword.Text = builder.Password;
                }
            }
            catch
            {
                // If we can't load current config, start with empty form
            }
        }

        private void BtnTest_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                try
                {
                    var builder = new SqlConnectionStringBuilder
                    {
                        DataSource = txtServer.Text.Trim(),
                        InitialCatalog = txtDatabase.Text.Trim(),
                        UserID = txtUsername.Text.Trim(),
                        Password = txtPassword.Text,
                        Encrypt = true,
                        TrustServerCertificate = false,
                        ConnectTimeout = 10
                    };

                    using (var connection = new SqlConnection(builder.ConnectionString))
                    {
                        connection.Open();
                        MessageBox.Show("Connection successful!", "Test Connection",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Connection failed: {ex.Message}", "Test Connection",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                var builder = new SqlConnectionStringBuilder
                {
                    DataSource = txtServer.Text.Trim(),
                    InitialCatalog = txtDatabase.Text.Trim(),
                    UserID = txtUsername.Text.Trim(),
                    Password = txtPassword.Text,
                    Encrypt = true,
                    TrustServerCertificate = false
                };

                ConnectionString = builder.ConnectionString;
            }
            else
            {
                this.DialogResult = DialogResult.None;
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtServer.Text))
            {
                MessageBox.Show("Please enter a server name.", "Validation Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtServer.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtDatabase.Text))
            {
                MessageBox.Show("Please enter a database name.", "Validation Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtDatabase.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                MessageBox.Show("Please enter a username.", "Validation Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtUsername.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                MessageBox.Show("Please enter a password.", "Validation Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPassword.Focus();
                return false;
            }

            return true;
        }
    }
}
