﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
//#if TargetYear2025
using Microsoft.Data.SqlClient;
//using Microsoft.Data.SqlClient;
//#else
//using System.Data.SqlClient;
//#endif

namespace BecaAzureSQL
{
    /// <summary>
    /// This class represents a simple wrapper for creating SqlConnection instances.
    /// </summary>
    public class DatabaseConnection
    {
        private readonly string _connectionString;

        public DatabaseConnection(string connectionString)
        {
            this._connectionString = connectionString;
        }

        public SqlConnection CreateConnection()
        {
            SqlConnection sqlCon = new SqlConnection(this._connectionString);
            return sqlCon;
        }
    }
}
