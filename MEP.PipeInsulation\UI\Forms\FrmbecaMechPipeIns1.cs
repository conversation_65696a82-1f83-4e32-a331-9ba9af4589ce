﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Plumbing;
using BecaRevitUtilities;
using BecaTransactionsNamesManager;
using Common.UI.Forms;
using MEP.PipeInsulation.CoreLogic;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MEP.PipeInsulation.UI.Forms
{
    public partial class FrmbecaMechPipeIns1 : BecaBaseForm
    {
        #region Fields
        Document _doc;
        ElementId _pipeInsulationTypeId;
        IList<Element> _pipeInsulations;
        IList<Reference> _SelectedItems;

        #endregion

        #region Constructors

        public FrmbecaMechPipeIns1(Document doc, IList<Reference> selectedItems)
        {
            InitializeComponent();
            _doc = doc;
            lblSelectedIndexRun.Text = $"This tool will automatically add insulation to {selectedItems.Count} currently selected pipes and pipe fittings\nin the project.";
            _pipeInsulationTypeId = new FilteredElementCollector(doc).OfClass(typeof(PipeInsulationType)).ToElements().ElementAt(0).Id;
            _pipeInsulations = new FilteredElementCollector(doc).OfClass(typeof(Autodesk.Revit.DB.Plumbing.PipeInsulation)).ToElements();
            _SelectedItems = selectedItems;

        }

        #endregion

        private void btn_Start_Click(object sender, EventArgs e)
        {
            try
            {
                //Process each element in the selection
                int rqI = 0;
                int rqJ = 0;
                double rqPipeInsThickness = 0;
                double rqPipeDia = 0;
                string rqPipeSysAbbr = "";
                int ignored = 0;
                var nameGroup = new List<string>();

                using (var trans = new Transaction(_doc, BecaTransactionsNames.PipeInsulation_AddingAllInsulationsbasedonrules.GetHumanReadableString()))
                {
                    trans.Start();

                    int nCount = _SelectedItems.Count;
                    string progressMessage = "{0} of " + nCount.ToString() + " elements processed...";
                    string caption = "Processing elements";
                    using (var pf = new Common.UI.Forms.BecaProgressForm(caption, progressMessage, nCount))
                    {
                        foreach (Reference r in _SelectedItems)
                        {
                            Element rqPipeOrFittingElem = _doc.GetElement(r.ElementId) as Element;

                            //Find if the current selected element has insulation, then get the insulation element
                            Autodesk.Revit.DB.Plumbing.PipeInsulation rqExPipeIns = null;
                            foreach (Autodesk.Revit.DB.Plumbing.PipeInsulation insulation in _pipeInsulations)
                            {
                                if (insulation.HostElementId == rqPipeOrFittingElem.Id)
                                {
                                    rqExPipeIns = insulation;
                                    break;
                                }
                            }


                            Parameter rqParaSysName = rqPipeOrFittingElem.get_Parameter(BuiltInParameter.RBS_SYSTEM_NAME_PARAM);
                            rqPipeSysAbbr = rqParaSysName.AsString().Substring(0, 3);
                            switch (rqPipeSysAbbr)
                            {
                                case "CHW":
                                case "HHW":
                                case "DC":
                                case "RF":
                                    if ("Pipes" == rqPipeOrFittingElem.Category.Name.ToString())
                                    {
                                        Pipe rqPipe = rqPipeOrFittingElem as Pipe;
                                        rqPipeDia = RevitUnitConvertor.MmToInternal(rqPipe.Diameter);
                                        /*
                                        PipingSystem rqPipingSys = rqPipe.MEPSystem as PipingSystem;
                                        rqPipeSysAbbr = rqPipingSys.Name.ToString().Substring(0, 3);
                                        */
                                        rqI++;
                                    }
                                    else
                                    {
                                        Parameter rqParaDiameter = rqPipeOrFittingElem.LookupParameter("Nominal Diameter");
                                        if (null == rqParaDiameter)
                                        {
                                            rqParaDiameter = rqPipeOrFittingElem.LookupParameter("Nominal Diameter 1");
                                        }
                                        if (null != rqParaDiameter)
                                        {
                                            rqPipeDia = RevitUnitConvertor.MmToInternal(rqParaDiameter.AsDouble());
                                        }
                                        rqJ++;
                                    }
                                    rqPipeInsThickness = Helper.rqGetInsThickness(rqPipeSysAbbr, rqPipeDia);
                                    Helper.rqAddPipeIns(rqExPipeIns, _doc, rqPipeInsThickness, rqPipeOrFittingElem, _pipeInsulationTypeId, false);
                                    nameGroup.Add(rqPipeSysAbbr);
                                    pf.Increment();
                                    break;
                                default:
                                    ignored++;
                                    nameGroup.Add(Helper.Ignored);
                                    pf.Increment();
                                    break;
                            }
                        }
                    }

                    trans.Commit();
                }

                // Show result, groupped and counted
                var groupedAndCounted = Helper.GroupAndCount(nameGroup);
                var summaryText = string.Join("\n", groupedAndCounted.Select(kvp => $"{kvp.Key}: {kvp.Value}"));
                BecaBaseMessageForm frmEnd = new BecaBaseMessageForm("\nPipes Processed: " + rqI + "  |||   Pipe Fittings Processed: " + rqJ + "\n\n" + summaryText, "PIPE INSULATION RESULT");
                frmEnd.OkButtonText = "Finish";
                frmEnd.HideCancelButton = false;
                frmEnd.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        protected override void btnHelp_Click(object sender, EventArgs e)
        {
            System.Diagnostics.Process.Start("https://becagroup.sharepoint.com/KnowledgeCentre/Buildings/BIMBrilliance/Pages/Pipe%20Insulation%20Placer.aspx ");
        }
    }
}
