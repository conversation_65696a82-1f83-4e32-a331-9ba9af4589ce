﻿using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PipeInsulation.UI.Views.ViewHandlers
{
    public class RequestHandler : IExternalEventHandler
    {
        BecaActivityLoggerData _logger;

        RequestPipeInsulationHandler _request = new RequestPipeInsulationHandler();
        public RequestPipeInsulationHandler Request { get { return _request; } }

        public RequestHandler(BecaActivityLoggerData logger)
        {
            _logger = logger;
        }

        public void Execute(UIApplication uiapp)
        {
            var doc = uiapp.ActiveUIDocument.Document;
            try
            {
                switch (Request.Take())
                {
                    case RequestId.Close:
                        {
                            // Add close handler if needed
                            break;
                        }
                    case RequestId.AddInsulations:
                        {
                            PI_MainWindowHandler.AddInsulations(uiapp, _logger);
                            break;
                        }
                    case RequestId.SelectElementsFromRevit:
                        {
                            PI_MainWindowHandler.SelectElementsInModel(uiapp, _logger); 
                            break;
                        }
                    case RequestId.SelectSystemFromRevit:
                        {
                            PI_MainWindowHandler.SelectOneElementInModel(uiapp, _logger); 
                            break;
                        }
                    case RequestId.Create3DView:
                        {
                            PI_MainWindowHandler.Create3DViewHandler(uiapp, _logger); 
                            break;
                        }
                    case RequestId.DetectInteriorExterior:
                        {
                            PI_MainWindowHandler.DetectInteriorExterior(uiapp, _logger);
                            break;
                        }
                    case RequestId.RemoveAllInsulations:
                        {
                            PI_MainWindowHandler.RemoveAllInsulations(uiapp, _logger);
                            break;
                        }
                    case RequestId.UpdatePI_SelectedDataSource:
                        {
                            PI_MainWindowHandler.UpdateSelectedDataSource(uiapp, _logger);
                            break;
                        }
                    default:
                        {
                            // some kind of a warning here should
                            // notify us about an unexpected request 
                            break;
                        }
                }
            }
            finally
            {

            }

            return;
        }

        public string GetName()
        {
            return "Firza U";
        }
    }
}
