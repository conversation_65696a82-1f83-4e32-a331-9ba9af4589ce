﻿using MEP.PipeInsulation.CoreLogic;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MEP.PipeInsulation.UI.Forms
{
    public partial class FrmDatabaseSettings : Form
    {
        List<PI_Database> _selectedDatabase;
        string _projectSettingsExportPathCustom;
        PI_DatabaseJsonHandler _jsonHandler;

        bool _isCustom;
        bool _projectFolderExist;
        string _projectDirectoryName;

        string _tempTypeName;
        string _typeToShowInList;

        public List<PI_Database> CustomDatabase { get; set; }
        public bool TypeFound { get; set; }

        public FrmDatabaseSettings(string typeToShowInList, List<PI_Database> selectedDatabase, string projectSettingsExportPathCustom, bool isCustom)
        {
            InitializeComponent();

            TypeFound = false;

            _typeToShowInList = typeToShowInList;

            _jsonHandler = new PI_DatabaseJsonHandler();
            _projectSettingsExportPathCustom = projectSettingsExportPathCustom;
            _isCustom = isCustom;

            SetSelectedDatabase(selectedDatabase);
            
            SetTitle();

            PopulateDatabase();

            CheckProjectDirectory(_projectSettingsExportPathCustom);
        }

        private void SetSelectedDatabase(List<PI_Database> selectedDatabase)
        {
            _selectedDatabase = selectedDatabase;

            if (_isCustom && File.Exists(_projectSettingsExportPathCustom))
            {
                _selectedDatabase = _jsonHandler.ImportFromJson(_projectSettingsExportPathCustom);
            }

            CustomDatabase = _selectedDatabase;
        }

        private void CheckProjectDirectory(string projectSettingsExportPathCustom)
        {
            _projectFolderExist = true;

            var projectDirectoryName = Path.GetDirectoryName(projectSettingsExportPathCustom);
            if (!Directory.Exists(projectDirectoryName))
            {
                _projectFolderExist = false;
                Directory.CreateDirectory(projectDirectoryName);
            }
        }

        private void SetTitle()
        {
            if (_isCustom)
                lbl_CountryReference.Text = "Custom Settings";
            else
                lbl_CountryReference.Text = $"{_selectedDatabase.FirstOrDefault().CountryReference} Settings";
        }

        private void PopulateDatabase()
        {
            foreach (var db in _selectedDatabase)
            {
                if (db.PipeType != _typeToShowInList)
                    continue;

                dgv_DatabaseSettings.Rows.Add(
                    db.PipeType,
                    db.SystemSize,
                    db.MinDiameter,
                    db.MaxDiameter,
                    db.InteriorElastometric,
                    db.InteriorPolyethelene,
                    db.InteriorPhenolic,
                    db.ExteriorElastometric,
                    db.ExteriorPolyethelene,
                    db.ExteriorPhenolic);
            }

            if (dgv_DatabaseSettings.Rows.Count < 2)
            {
                MessageBox.Show($"Can't find '{_typeToShowInList}' in the database.", "Unmatched type.");
                return;
            }
            else
            {
                TypeFound = true;
            }

            dgv_DatabaseSettings.Columns[0].HeaderText = "\nPipe Type\n";
            foreach (DataGridViewColumn col in dgv_DatabaseSettings.Columns)
            {
                dgv_DatabaseSettings.Columns[col.Index].HeaderCell.Style.BackColor = Color.FromArgb(18, 168, 178);
                dgv_DatabaseSettings.Columns[col.Index].HeaderCell.Style.ForeColor = Color.White;
            }

            if (!_isCustom && _selectedDatabase.First().CountryReference != CountryReference.Custom.ToString())
            {
                btn_MoveUp.Visible = false;
                btn_MoveDown.Visible = false;
                btn_SaveSettings.Visible = false;
                foreach (DataGridViewColumn column in dgv_DatabaseSettings.Columns)
                {
                    column.ReadOnly = true;
                    column.DefaultCellStyle.BackColor = Color.LightGray;
                    column.DefaultCellStyle.ForeColor = Color.Gray;
            
                }

            }

        }

        private void dgv_DatabaseSettings_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.ColumnIndex > -1 && _isCustom && dgv_DatabaseSettings.Columns[e.ColumnIndex].Index == PipeType.Index)
            {
                _tempTypeName = dgv_DatabaseSettings[e.ColumnIndex, e.RowIndex].Value?.ToString();

                DataGridViewComboBoxCell typesDropdown = new DataGridViewComboBoxCell();
                typesDropdown.DataSource = _selectedDatabase.Select(x => x.PipeType).Distinct().ToList();
                dgv_DatabaseSettings[e.ColumnIndex, e.RowIndex] = typesDropdown;

            }

            if (e.ColumnIndex > -1 && _isCustom && dgv_DatabaseSettings.Columns[e.ColumnIndex].Index == SystemSize.Index)
            {
                DataGridViewComboBoxCell systemSizeDropdown = new DataGridViewComboBoxCell();
                systemSizeDropdown.DataSource = new List<string>() { "", ">250kW", "<250kW" };
                dgv_DatabaseSettings[e.ColumnIndex, e.RowIndex] = systemSizeDropdown;

            }
        }

        

        private void btn_SaveSettings_Click(object sender, EventArgs e)
        {
            var pI_Databases = new List<PI_Database>();
            for (int i = 0; i < dgv_DatabaseSettings.Rows.Count - 1; i++)
            {
                var pI_Database = new PI_Database()
                {
                    PipeType = dgv_DatabaseSettings.Rows[i].Cells[PipeType.Index]?.Value?.ToString() ?? string.Empty,
                    SystemSize = dgv_DatabaseSettings.Rows[i].Cells[SystemSize.Index]?.Value?.ToString() ?? string.Empty,
                    MinDiameter = Convert.ToDouble(dgv_DatabaseSettings.Rows[i].Cells[MinDiameter.Index].Value?.ToString() ?? "0"),
                    MaxDiameter = Convert.ToDouble(dgv_DatabaseSettings.Rows[i].Cells[MaxDiameter.Index]?.Value?.ToString() ?? string.Empty ?? "0"),
                    InteriorElastometric = Convert.ToDouble(dgv_DatabaseSettings.Rows[i].Cells[InteriorElastometric.Index]?.Value?.ToString() ?? string.Empty ?? "0"),
                    InteriorPolyethelene = Convert.ToDouble(dgv_DatabaseSettings.Rows[i].Cells[InteriorPhenolic.Index]?.Value?.ToString() ?? string.Empty ?? "0"),
                    InteriorPhenolic = Convert.ToDouble(dgv_DatabaseSettings.Rows[i].Cells[ExteriorElastometric.Index]?.Value?.ToString() ?? string.Empty ?? "0"),
                    ExteriorElastometric = Convert.ToDouble(dgv_DatabaseSettings.Rows[i].Cells[ExteriorElastometric.Index]?.Value?.ToString() ?? string.Empty ?? "0"),
                    ExteriorPolyethelene = Convert.ToDouble(dgv_DatabaseSettings.Rows[i].Cells[ExteriorPolyethelene.Index]?.Value?.ToString() ?? string.Empty ?? "0"),
                    ExteriorPhenolic = Convert.ToDouble(dgv_DatabaseSettings.Rows[i].Cells[ExteriorPhenolic.Index]?.Value?.ToString() ?? string.Empty ?? "0")
                };
                if (String.IsNullOrEmpty(pI_Database.PipeType))
                {
                    MessageBox.Show("Null Pipe Type found.\nMake sure all types are set");
                    return;
                }
                pI_Databases.Add(pI_Database);
            }

            CustomDatabase = pI_Databases;

            _jsonHandler.Settings = pI_Databases;
            _jsonHandler.ExportToJson(_projectSettingsExportPathCustom);
            _jsonHandler.Settings = null;

            if (!_projectFolderExist)
            {
                MessageBox.Show($"Save Successful:\n\nSettings have been saved to '{_projectSettingsExportPathCustom}'\n\nAs no settings file was present, we've created one now.");
                _projectFolderExist = true;
            }
            else
            {
                MessageBox.Show($"Save Successful:\n\nSettings have been saved to '{_projectSettingsExportPathCustom}'\n\nThe existing settings file for this project has been updated. ");
            }
        }

        private void contextMenuStrip1_Click(object sender, EventArgs e)
        {
            foreach (DataGridViewRow row in dgv_DatabaseSettings.SelectedRows)
            {
                if (!row.IsNewRow)
                {
                    dgv_DatabaseSettings.Rows.RemoveAt(row.Index);
                }
            }
        }

        private void dgv_DatabaseSettings_CellMouseUp(object sender, DataGridViewCellMouseEventArgs e)
        {
            if (_isCustom && e.Button == MouseButtons.Right)
            {
                this.contextMenuStrip1.Show(dgv_DatabaseSettings, e.Location);
                contextMenuStrip1.Show(Cursor.Position);
            }
        }

        private void btn_MoveUp_Click(object sender, EventArgs e)
        {
            try
            {
                DataGridViewSelectedRowCollection selectedRows = dgv_DatabaseSettings.SelectedRows;
                if (selectedRows.Count == 0)
                {
                    MessageBox.Show("Select a full row to move");
                    return;
                }
                else if (selectedRows.Count > 1)
                {
                    MessageBox.Show("Select one full row to move");
                    return;
                }
                    
                int rowCount = dgv_DatabaseSettings.Rows.Count;

                // Move each selected row up one position at a time
                foreach (DataGridViewRow row in selectedRows)
                {
                    int rowIndex = row.Index;
                    if (rowIndex > 0)
                    {
                        // Remove the row from its current position
                        dgv_DatabaseSettings.Rows.Remove(row);

                        // Reinsert the row at the desired position
                        dgv_DatabaseSettings.Rows.Insert(rowIndex - 1, row);

                        // Update the selection to maintain the selection of the moved row
                        dgv_DatabaseSettings.Rows[rowIndex - 1].Selected = true;
                    }
                }
            }
            catch (Exception ex)
            {

            }
        }
        private void btn_MoveDown_Click(object sender, EventArgs e)
        {
            try
            {
                DataGridViewSelectedRowCollection selectedRows = dgv_DatabaseSettings.SelectedRows;
                if (selectedRows.Count == 0)
                {
                    MessageBox.Show("Select a full row to move");
                    return;
                }
                else if (selectedRows.Count > 1)
                {
                    MessageBox.Show("Select one full row to move");
                    return;
                }
                int rowCount = dgv_DatabaseSettings.Rows.Count;

                // Move each selected row down one position at a time
                foreach (DataGridViewRow row in selectedRows.Cast<DataGridViewRow>().Reverse())
                {
                    int rowIndex = row.Index;
                    if (rowIndex < rowCount - 1)
                    {
                        // Remove the row from its current position
                        dgv_DatabaseSettings.Rows.Remove(row);

                        // Reinsert the row at the desired position
                        dgv_DatabaseSettings.Rows.Insert(rowIndex + 1, row);

                        // Update the selection to maintain the selection of the moved row
                        dgv_DatabaseSettings.Rows[rowIndex + 1].Selected = true;
                    }
                }

            }
            catch (Exception ex)
            {

            }
        }

        private void dgv_DatabaseSettings_CellEndEdit(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.ColumnIndex == dgv_DatabaseSettings.Columns[PipeType.Index].Index && String.IsNullOrEmpty(dgv_DatabaseSettings[e.ColumnIndex, e.RowIndex].Value?.ToString()))
                    dgv_DatabaseSettings[e.ColumnIndex, e.RowIndex].Value = _tempTypeName;
                if (e.ColumnIndex == dgv_DatabaseSettings.Columns[SystemSize.Index].Index && String.IsNullOrEmpty(dgv_DatabaseSettings[e.ColumnIndex, e.RowIndex].Value?.ToString()))
                    dgv_DatabaseSettings[e.ColumnIndex, e.RowIndex] = new DataGridViewTextBoxCell();
            }
            catch (Exception)
            {

            }
        }
    }

    public class PI_DatabaseJsonHandler
    {
        public List<PI_Database> Settings { get; set; }

        // Export data to a JSON file
        public void ExportToJson(string filePath)
        {
            string json = JsonConvert.SerializeObject(Settings, Formatting.Indented);
            // Clear the existing content of the file
            File.WriteAllText(filePath, string.Empty);

            // Write the new JSON content to the file
            File.WriteAllText(filePath, json);
        }

        // Import data from a JSON file
        public List<PI_Database> ImportFromJson(string filePath)
        {
            string json = File.ReadAllText(filePath);
            return JsonConvert.DeserializeObject<List<PI_Database>>(json);
        }
    }
}
