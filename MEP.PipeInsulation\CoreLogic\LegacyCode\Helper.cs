﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using BecaRevitUtilities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TaskDialog = Autodesk.Revit.UI.TaskDialog;

namespace MEP.PipeInsulation.CoreLogic
{
    public static class Helper
    {
        public static string Ignored = "Ignored";
        
        //Find Insulation Thickness
        public static double rqGetInsThickness(string subStrSysAbbr, double subDiameter)
        {
            double subInsThickness = 0;
            switch (subStrSysAbbr)
            {
                case "CHW":
                    if (subDiameter < 40)
                    {
                        subInsThickness = 25;
                    }
                    else
                    {
                        if (subDiameter < 81)
                        {
                            subInsThickness = 44;
                        }
                        else
                        {
                            if (subDiameter < 151)
                            {
                                subInsThickness = 60;
                            }
                            else
                            { subInsThickness = 75; }
                        }
                    }
                    break;



                case "HHW":
                    if (subDiameter < 26)
                    {
                        subInsThickness = 40;
                    }
                    else
                    {
                        if (subDiameter < 61)
                        {
                            subInsThickness = 50;
                        }
                        else
                        {
                            if (subDiameter < 126)
                            {
                                subInsThickness = 50;
                            }
                            else
                            {
                                if (subDiameter < 501)
                                {
                                    subInsThickness = 63;
                                }
                                else
                                { subInsThickness = 63; }
                            }
                        }
                    }
                    break;



                case "DC ":
                    if (subDiameter < 15)
                    {
                        subInsThickness = 0;
                    }
                    else if(subDiameter < 66)
                    {
                        subInsThickness = 63;
                    }
                    else if(subDiameter < 151)
                    {
                        subInsThickness = 70;
                    }
                    else if(subDiameter < 401)
                    {
                        subInsThickness = 80;
                    }
                    break;



                case "RF ":
                    if (subDiameter < 40)
                    {
                        subInsThickness = 25;
                    }
                    else if(subDiameter < 81)
                    {
                        subInsThickness = 44;
                    }
                    else if(subDiameter < 151)
                    {
                        subInsThickness = 60;
                    }
                    else if(subDiameter < 601)
                    {
                        subInsThickness = 75;
                    }
                    break;


            }

            return subInsThickness;
        }

        //Edit existing insulation "exLine" or add a new piece of insulation to a piece of pipe or pipefitting, based on "subThickness"

        static public void rqAddPipeIns(Autodesk.Revit.DB.Plumbing.PipeInsulation exPipeIns, Document subDoc, double subThickness, Element subPipeOrFitting, ElementId subInsTypeID, bool subShowDia)
        {
            Transaction rqSubTrans = new Transaction(subDoc);

            if (0 != subThickness)
            {
                if (exPipeIns != null)
                {

                    //rqSubTrans.Start("Edit Existing Lining");
                    exPipeIns.Thickness = RevitUnitConvertor.MmToInternal( subThickness) ;
                    // rqSubTrans.Commit();
                }
                else
                {
                    // rqSubTrans.Start("Add Lining: " + subThickness.ToString() + "mm");
                    Autodesk.Revit.DB.Plumbing.PipeInsulation.Create(subDoc, subPipeOrFitting.Id, subInsTypeID, RevitUnitConvertor.MmToInternal(subThickness));
                    // rqSubTrans.Commit();
                    if (subShowDia)
                    {
                        TaskDialog.Show("Beca Mechanical Tools", "**** " + subThickness.ToString() + "mm" + " Insulation Added");
                    }
                }
                /*
                //Post Process Tee insulation parameter
                if (subPipeOrFitting.Category.Name.ToString()=="Pipe Fittings")
                {
                    FamilyInstance rqPipeFitting = subPipeOrFitting as FamilyInstance;
                    if (rqPipeFitting.Symbol.FamilyName== "Beca-74-Pipe Tee-Adjust Length")
                    {
                        Guid rqGuidInsuThickness = Guid.Parse("108ec1e9-a229-4866-9387-1cb5c321c93a");
                        Parameter rqParaBecaInsThk = rqPipeFitting.get_Parameter(rqGuidInsuThickness);
                        rqParaBecaInsThk.Set(becaMechDuctType1.rqDoubleToMilimeter(subThickness));
                    }
                }
                */
                /*
                if (null != subPipeOrFitting.get_Parameter(rqGuidInsuThickness))
                {
                    // "Beca Insulation Thickness"  --  108ec1e9-a229-4866-9387-1cb5c321c93a
                    Parameter rqParaBecaInsThk = subPipeOrFitting.get_Parameter(rqGuidInsuThickness);
                    rqParaBecaInsThk.Set(becaMechDuctType1.rqDoubleToMilimeter(subThickness));
                }
                */
            }
        }

        public static Dictionary<string, int> GroupAndCount1(List<string> inputList)
        {
            Dictionary<string, int> result = new Dictionary<string, int>();

            foreach (string item in inputList)
            {
                if (result.ContainsKey(item))
                {
                    result[item]++;
                }
                else
                {
                    result[item] = 1;
                }
            }

            return result;
        }

        public static SortedDictionary<string, int> GroupAndCount(List<string> inputList)
        {
            SortedDictionary<string, int> result = new SortedDictionary<string, int>(new CustomComparer());

            foreach (string item in inputList)
            {
                if (result.ContainsKey(item))
                {
                    result[item]++;
                }
                else
                {
                    result[item] = 1;
                }
            }

            return result;
        }

    }

    class CustomComparer : IComparer<string>
    {
        public int Compare(string x, string y)
        {
            if (x == Helper.Ignored && y != Helper.Ignored)
                return 1;
            if (x != Helper.Ignored && y == Helper.Ignored)
                return -1;
            return x.CompareTo(y);
        }
    }
}
