﻿using System.Globalization;
using System.IO;
using Microsoft.VisualBasic.FileIO;

namespace MEP.PipeInsulation.CoreLogic
{
    public enum CountryReference
    {
        NZ,
        AU,
        SG,
        Custom
    }

    public enum InsulationType
    {
        WorstCase,
        Elastometric,
        Polyethylene,
        Phenolic
    }

    public static class PI_DefaultSettings
    {
        public static string DatabasePath = Path.Combine(Path.GetFullPath(Path.Combine(System.Reflection.Assembly.GetExecutingAssembly().Location, @"../../../../")),
            "Addins", "MEP Tools", "PipeInsulation Database", "Pipe Insulation Tool - Database.xlsx");
        public static string DatabaseFolderPath = Path.Combine(Path.GetFullPath(Path.Combine(System.Reflection.Assembly.GetExecutingAssembly().Location, @"../../../../")),
            "Addins", "MEP Tools", "PipeInsulation Database") + Path.DirectorySeparatorChar;
        public static string ProjectSettingsPath = @"P:\171\1711555\Projects\PipeInsulation";

        public static string SharedParameterPath = Path.Combine(Path.GetFullPath(Path.Combine(System.Reflection.Assembly.GetExecutingAssembly().Location, @"../../../../")),
            "Addins", "MEP Tools", "_Shared Parameters");

        public const string ProjectInfoParameterName_DatabaseLocation = "Beca_Pipe_Insulation_Database_Location";

        public static List<List<PI_Database>> ReadExcelDatabaseFile(string csvFolderPath)
        {
            List<List<PI_Database>> SizesAndThickness = new List<List<PI_Database>>();

            string[] csvFileNames = { "NZ.csv", "AU.csv", "SG.csv" };
            List<string> cvsFiles = new List<string>();
            foreach (var fileName in csvFileNames)
            {
                string fullPath = Path.Combine(csvFolderPath, fileName);
                if (File.Exists(fullPath))
                {
                    cvsFiles.Add(fullPath);
                }
            }

            foreach (var filePath in cvsFiles)
            {
                var database = new List<PI_Database>();

                using (TextFieldParser parser = new TextFieldParser(filePath))
                {
                    parser.TextFieldType = FieldType.Delimited;
                    parser.SetDelimiters(",");
                    while (!parser.EndOfData)
                    {
                        //Processing row
                        string[] fields = parser.ReadFields();
                        database.Add(new PI_Database
                        {
                            CountryReference = fields[0],
                            PipeType = fields[1],
                            SystemSize = fields[2],
                            MinDiameter = TryParseDouble(fields[3]),
                            MaxDiameter = TryParseDouble(fields[4]),
                            InteriorElastometric = TryParseDouble(fields[5]),
                            InteriorPolyethelene = TryParseDouble(fields[6]),
                            InteriorPhenolic = TryParseDouble(fields[7]),
                            ExteriorElastometric = TryParseDouble(fields[8]),
                            ExteriorPolyethelene = TryParseDouble(fields[9]),
                            ExteriorPhenolic = TryParseDouble(fields[10])
                        });
                    }
                }
                SizesAndThickness.Add(database);
            }

            return SizesAndThickness;
        }

        private static double TryParseDouble(string value)
        {
            double.TryParse(value, NumberStyles.Any, CultureInfo.InvariantCulture, out double result);
            return result;
        }

        private static void ReleaseObject(object obj)
        {
            try
            {
                System.Runtime.InteropServices.Marshal.ReleaseComObject(obj);
                obj = null;
            }
            catch (Exception ex)
            {
                obj = null;
                Console.WriteLine("Exception occurred while releasing object: " + ex.ToString());
            }
            finally
            {
                GC.Collect();
            }
        }

    }
}
