# Common Secure Configuration Management

This folder contains the common secure configuration management system for handling encrypted database connection strings across multiple Beca Revit projects.

## Overview

The common secure configuration system provides:
- **Centralized configuration management** - One source for secure connection string handling
- **Enhanced security** - Dynamic encryption key generation instead of hardcoded keys
- **Project flexibility** - Support for multiple projects with different configuration files
- **Migration support** - Tools to migrate from project-specific implementations

## Key Components

### 1. SecureConfigManager.cs
The core class that handles encryption/decryption of connection strings.

**Key Features:**
- Dynamic encryption key generation using algorithmic approach
- Support for custom config file names
- Fallback to app.config for development/testing
- AES encryption with randomly generated IVs

### 2. SecureConnectionProvider.cs
Provides easy-to-use connection management with secure configuration.

**Key Features:**
- Factory pattern for project-specific providers
- Integration with existing DatabaseConnection class
- Direct SqlConnection creation
- Configuration validation

### 3. ConfigurationMigrationUtility.cs
Helps migrate from old project-specific secure configuration managers.

**Key Features:**
- Migration from old hardcoded key systems
- Backup and restore functionality
- Validation of migrated configurations

## Usage Examples

### Basic Usage
```csharp
// Create a provider for your project
var provider = SecureConnectionProviderFactory.CreateProjectProvider("MyProject", "MyConnectionString");

// Get connection string
string connectionString = provider.GetConnectionString();

// Create database connection
var dbConnection = provider.CreateDatabaseConnection();

// Create SQL connection directly
using var sqlConnection = provider.CreateSqlConnection();
```

### Pipe Insulation Project
```csharp
// Use the pre-configured provider for Pipe Insulation
var provider = SecureConnectionProviderFactory.CreatePipeInsulationProvider();

// This will look for "PipeInsulation.config" and fallback to "PipeInsulationDB" in app.config
string connectionString = provider.GetConnectionString();
```

### Creating Configuration
```csharp
var provider = SecureConnectionProviderFactory.CreateProjectProvider("MyProject");

// Create configuration with connection details
bool success = provider.CreateConfiguration(
    server: "myserver.database.windows.net",
    database: "mydatabase",
    username: "myuser",
    password: "mypassword"
);

// Or create from existing connection string
provider.CreateEncryptedConfig("Server=myserver;Database=mydatabase;...");
```

## Security Improvements

### Dynamic Key Generation
The new system uses algorithmic key generation instead of hardcoded keys:

```csharp
// Old approach (hardcoded)
const string companyKey = "RKE1jyALPgtUHsh2UKFdcG1HoBOyCdA7";

// New approach (algorithmic)
private static string GenerateCompanyKey()
{
    // Uses mathematical transformations on seed arrays
    // Makes reverse engineering significantly harder
    // Still deterministic for consistent decryption
}
```

### Enhanced Encryption
- Uses AES encryption with SHA256-hashed keys
- Randomly generated IVs for each encryption
- IV prepended to encrypted data for secure storage

## Migration from Old Systems

### Automatic Migration
```csharp
// Migrate from old PI_SecureConfigManager
bool success = ConfigurationMigrationUtility.MigratePipeInsulationConfig();

// Validate migration worked
bool isValid = ConfigurationMigrationUtility.ValidateMigration();
```

### Manual Migration
1. **Backup existing configuration:**
   ```csharp
   string backupPath = ConfigurationMigrationUtility.CreateConfigBackup();
   ```

2. **Run migration:**
   ```csharp
   bool success = ConfigurationMigrationUtility.MigratePipeInsulationConfig(preserveOriginal: false);
   ```

3. **Validate and test:**
   ```csharp
   var provider = SecureConnectionProviderFactory.CreatePipeInsulationProvider();
   bool isWorking = provider.ValidateConfiguration();
   ```

4. **Restore if needed:**
   ```csharp
   if (!isWorking && backupPath != null)
   {
       ConfigurationMigrationUtility.RestoreFromBackup(backupPath);
   }
   ```

## Integration with Existing Code

### Updating PI_DatabaseClient.cs
Replace the old secure config manager usage:

```csharp
// Old approach
string connectionString = PI_SecureConfigManager.GetConnectionString();

// New approach (when common library is referenced)
var provider = SecureConnectionProviderFactory.CreatePipeInsulationProvider();
string connectionString = provider.GetConnectionString();
```

### Updating Configuration Setup
```csharp
// Old approach
PI_SecureConfigManager.CreateEncryptedConfig(connectionString);

// New approach
var provider = SecureConnectionProviderFactory.CreatePipeInsulationProvider();
provider.CreateEncryptedConfig(connectionString);
```

## Configuration Files

### Default Locations
- **Default config:** `BecaRevit.config`
- **Pipe Insulation:** `PipeInsulation.config`
- **Custom projects:** `{ProjectName}.config`

### File Format
Configuration files contain encrypted connection strings in Base64 format. The encryption includes:
- AES-encrypted connection string
- Prepended IV for secure decryption
- SHA256-hashed algorithmic key

## Best Practices

1. **Use project-specific providers** for better organization
2. **Always validate configuration** after creation or migration
3. **Create backups** before migration
4. **Test connections** after configuration changes
5. **Use app.config fallback** for development environments

## Troubleshooting

### Common Issues
1. **Configuration not found:** Ensure config file exists in assembly directory
2. **Decryption fails:** Check if migration is needed from old system
3. **Connection fails:** Validate connection string parameters
4. **Assembly reference errors:** Ensure common library is properly referenced

### Debug Steps
1. Check if config file exists: `File.Exists(configPath)`
2. Validate configuration: `provider.ValidateConfiguration()`
3. Test with app.config fallback
4. Check assembly location and permissions
