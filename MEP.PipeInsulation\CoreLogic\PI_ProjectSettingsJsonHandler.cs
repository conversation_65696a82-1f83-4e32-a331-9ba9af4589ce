﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PipeInsulation.CoreLogic
{
    public class PI_ProjectSettingsJsonHandler
    {
        public List<PI_ProjectSettings> Settings { get; set; }

        // Export data to a JSON file
        public void ExportToJson01(string filePath)
        {
            string json = JsonConvert.SerializeObject(Settings, Formatting.Indented);
            // Clear the existing content of the file
            File.WriteAllText(filePath, string.Empty);

            // Write the new JSON content to the file
            File.WriteAllText(filePath, json);
        }

        public void ExportToJson(string filePath)
        {
            if (File.Exists(filePath))
            {
                // Read the existing JSON content from the file
                string existingJson = File.ReadAllText(filePath);

                // Deserialize the existing JSON content into a list of PI_ProjectSettings objects
                List<PI_ProjectSettings> existingSettings = JsonConvert.DeserializeObject<List<PI_ProjectSettings>>(existingJson);

                // Create a dictionary to store the existing settings by TypeMark for easy lookup
                Dictionary<string, PI_ProjectSettings> existingSettingsDict = existingSettings.ToDictionary(s => s.InsulationSettings.FirstOrDefault()?.TypeMark);

                // Iterate over the new settings and update the existing ones or add new ones
                foreach (PI_ProjectSettings newSetting in Settings)
                {
                    string typeMark = newSetting.InsulationSettings.FirstOrDefault()?.TypeMark;

                    if (existingSettingsDict.ContainsKey(typeMark))
                    {
                        // Replace the existing PI_ProjectSettings object with the new one
                        existingSettingsDict[typeMark] = newSetting;
                    }
                    else
                    {
                        // Add the new PI_ProjectSettings object
                        existingSettingsDict.Add(typeMark, newSetting);
                    }
                }

                // Get the updated list of settings
                List<PI_ProjectSettings> updatedSettings = existingSettingsDict.Values.ToList();

                // Serialize the updated settings to JSON
                string updatedJson = JsonConvert.SerializeObject(updatedSettings, Formatting.Indented);

                // Write the updated JSON content to the file
                File.WriteAllText(filePath, updatedJson);
            }
            else
            {
                string json = JsonConvert.SerializeObject(Settings, Formatting.Indented);
                // Clear the existing content of the file
                File.WriteAllText(filePath, string.Empty);

                // Write the new JSON content to the file
                File.WriteAllText(filePath, json);
            }
            
        }

        // Import data from a JSON file
        public List<PI_ProjectSettings> ImportFromJson(string filePath)
        {
            if (!File.Exists(filePath))
                return null;

            string json = File.ReadAllText(filePath);
            return JsonConvert.DeserializeObject<List<PI_ProjectSettings>>(json);
        }
    }
}
