﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Plumbing;
using BecaRevitUtilities;
using BecaRevitUtilities.ElementUtilities;
using CommunityToolkit.Mvvm.ComponentModel;
using MEP.PipeInsulation.CoreLogic.LogicEnhanced;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PipeInsulation.Models
{
    public enum DesignLevel
    {
        Base,
        Enhanced
    }

    public partial class PI_Element: ObservableObject
    {
        private View3D _view3D;

        public Element Element { get; set; }
        public string FamilyName { get; set; } = string.Empty;
        public ElementType SystemType { get; set; }
        public string SystemTypeName { get; set; }
        public string SystemName { get; set; }
        public string CNC { get; set; }
        public double Diameter { get; set; }
        public double Thickness { get; set; }
        public string InsulationTypeToSet { get; set; }
        public bool IsLocked { get; set; } = false; 
        public bool IsSelected { get; set; } = false; 
        public string Owner { get; set; } = string.Empty;
        public string InsulationMaterial { get; set; }

        [ObservableProperty]
        private bool _hasInsulation;
        [ObservableProperty]
        private double _calculatedThickness = 0;
        [ObservableProperty]
        private double _revitThickness = 0;
        [ObservableProperty]
        private bool _isInterior = true;

        public bool IsThicknessMatch => Math.Abs(RevitThickness - CalculatedThickness) < 0.001;

        partial void OnRevitThicknessChanged(double oldValue, double newValue)
        {
            OnPropertyChanged(nameof(IsThicknessMatch));
        }

        partial void OnCalculatedThicknessChanged(double oldValue, double newValue)
        {
            OnPropertyChanged(nameof(IsThicknessMatch));
        }

        public PI_Element(Document doc, Element e, View3D view3D)
        {
            _view3D = view3D;
            Element = e;
            Owner = e.ElementOwner(doc);
            IsLocked = e.IsLocked(doc);

#if TargetYear2024 || TargetYear2025 || TargetYear2026
            if (e.Category.BuiltInCategory == BuiltInCategory.OST_PipeCurves)
#else
            if (e.Category.Id.IntegerValue == (int)BuiltInCategory.OST_PipeCurves)
#endif
            {
                //IsInterior = CheckInteriorForPipeCurve(e);
                Diameter = Math.Round(RevitUnitConvertor.InternalToMm((e as Pipe).Diameter), 2);
            }
            else
            {
                Diameter = Math.Round(FittingsDiameter(e), 2);
            }

            SystemType = doc.GetElement(e.get_Parameter(BuiltInParameter.RBS_PIPING_SYSTEM_TYPE_PARAM).AsElementId()) as ElementType;
            SystemTypeName = SystemType?.Name ?? string.Empty;
            SystemName = e.get_Parameter(BuiltInParameter.RBS_SYSTEM_NAME_PARAM)?.AsString();
            FamilyName = SystemType?.FamilyName + " | " + Element.Name;
            HasInsulation = CheckInsulation(e);
            RevitThickness = Math.Round(RevitUnitConvertor.InternalToMm(e.get_Parameter(BuiltInParameter.RBS_REFERENCE_INSULATION_THICKNESS).AsDouble()));

            // Interior as default, will be checked in UI selection (checking will take time on large model)
            IsInterior = true;
        }

        private double FittingsDiameter(Element e)
        {
            Parameter param = e.LookupParameter("Nominal Diameter") ?? e.LookupParameter("Nominal Diameter 1");

            if (param != null)
            {
                double nominalDiameter;
                if (double.TryParse(param.AsValueString(), out nominalDiameter))
                {
                    return nominalDiameter;
                }
            }

            return 0;
        }

        public static bool CheckInsulation(Element e)
        {
            if (String.IsNullOrEmpty(e.get_Parameter(BuiltInParameter.RBS_REFERENCE_INSULATION_TYPE).AsString()))
                return false;
            else
                return true;
        }

    }
}