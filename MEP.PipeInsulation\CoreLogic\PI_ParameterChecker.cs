﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Application = Autodesk.Revit.ApplicationServices.Application;
using TaskDialog = Autodesk.Revit.UI.TaskDialog;

namespace MEP.PipeInsulation.CoreLogic
{
    public class PI_ParameterChecker
    {
        private readonly List<string> _pIProjectInfoParameterNames = new List<string>()
        {
            PI_DefaultSettings.ProjectInfoParameterName_DatabaseLocation
        };

        public bool ParametersIsAllGood(Document doc, Application app, string sharedParameterFilePath)
        {
            List<string> missingProjectInfoParameters = CheckProjectInfoParameters(doc, _pIProjectInfoParameterNames);

            bool anyMissing = missingProjectInfoParameters.Any();

#if TargetYear2020 || TargetYear2021 || TargetYear2022
            BuiltInParameterGroup electricalGroup = BuiltInParameterGroup.PG_TEXT;
#else
            ForgeTypeId electricalGroup = GroupTypeId.Text;
#endif

            if (anyMissing)
            {
                TaskDialog mainDialog = new TaskDialog("Missing Parameters")
                {
                    MainInstruction = "The following parameters are missing:",
                    MainContent =
                        "Project Info Parameters:\n" + string.Join("\n", missingProjectInfoParameters) + "\n\n" +
                        "Do you want to create these parameters?",
                    CommonButtons = TaskDialogCommonButtons.Yes | TaskDialogCommonButtons.No,
                    DefaultButton = TaskDialogResult.Yes
                };

                TaskDialogResult result = mainDialog.Show();

                if (result == TaskDialogResult.Yes)
                {
                    bool success = true;

                    using (Transaction trans = new Transaction(doc, "Create Missing Parameters"))
                    {
                        trans.Start();

                        if (missingProjectInfoParameters.Any())
                        {
                            CategorySet categorySet = new CategorySet();
                            categorySet.Insert(doc.Settings.Categories.get_Item(BuiltInCategory.OST_ProjectInformation));

                            foreach (string paramName in missingProjectInfoParameters)
                            {
                                if (!AddProjectParameter(doc, app, paramName, categorySet, electricalGroup, true, sharedParameterFilePath))
                                {
                                    success = false;
                                    break;
                                }
                            }
                        }

                        if (success)
                        {
                            trans.Commit();

                            TaskDialog.Show("Parameters Created", "Missing parameters have been successfully created.");
                            return true;
                        }
                        else
                        {
                            trans.RollBack();
                            TaskDialog.Show("Error", "Failed to create some or all parameters.");
                            return false;
                        }
                    }
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return true;
            }
        }

        private List<string> CheckProjectInfoParameters(Document doc, List<string> parameterNames)
        {
            List<string> missingParameters = new List<string>();

            ProjectInfo projectInfo = doc.ProjectInformation;
            if (projectInfo != null)
            {
                foreach (string paramName in parameterNames)
                {
                    Parameter param = projectInfo.LookupParameter(paramName);
                    if (param == null)
                    {
                        missingParameters.Add(paramName);
                    }
                }
            }

            return missingParameters;
        }

#if TargetYear2020 || TargetYear2021 || TargetYear2022
        public static bool AddProjectParameter(Document doc, Application app, string parameterName, CategorySet categorySet, BuiltInParameterGroup parameterGroup, bool instanceBinding, string sharedParameterFilePath)
#else
        public static bool AddProjectParameter(Document doc, Application app, string parameterName, CategorySet categorySet, ForgeTypeId parameterGroup, bool instanceBinding, string sharedParameterFilePath)
#endif
        {
            try
            {
                // Set the shared parameters file
                app.SharedParametersFilename = sharedParameterFilePath;
                DefinitionFile defFile = app.OpenSharedParameterFile();
                if (defFile == null)
                {
                    TaskDialog.Show("Project Parameter error", "Missing shared parameter file.");
                    return false;
                }

                // Find the external definition by parameter name
                var externalDefinition = (from DefinitionGroup dg in defFile.Groups
                                          from ExternalDefinition d in dg.Definitions
                                          where d.Name == parameterName
                                          select d).FirstOrDefault();

                if (externalDefinition == null)
                {
                    TaskDialog.Show("Project Parameter error", "Parameter not found in shared parameter file.");
                    return false;
                }

                // Create binding for the parameter
                Autodesk.Revit.DB.Binding binding = instanceBinding
                    ? app.Create.NewInstanceBinding(categorySet)
                    : app.Create.NewTypeBinding(categorySet);

                BindingMap map = doc.ParameterBindings;

                // Insert the parameter into the document
                if (!map.Insert(externalDefinition, binding, parameterGroup))
                {
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                TaskDialog.Show("Error", $"Failed to add project parameter: {ex.Message}");
                return false;
            }
        }

    }
}
