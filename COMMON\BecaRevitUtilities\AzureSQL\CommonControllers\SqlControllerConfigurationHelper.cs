using System;
using System.IO;
using Microsoft.Data.SqlClient;

namespace BecaRevitUtilities.AzureSQL.CommonControllers
{
    /// <summary>
    /// Helper class for configuring secure connection strings for SqlControllerBase
    /// This bridges the gap between the common secure configuration system and the existing SqlControllerBase
    /// </summary>
    public static class SqlControllerConfigurationHelper
    {
        /// <summary>
        /// Creates encrypted configuration for SqlControllerBase with the specified catalogue
        /// </summary>
        /// <param name="catalogue">Database catalogue name (e.g., "DevaBot")</param>
        /// <param name="server">Database server</param>
        /// <param name="database">Database name (usually same as catalogue)</param>
        /// <param name="username">Database username</param>
        /// <param name="password">Database password</param>
        /// <returns>True if configuration was created successfully</returns>
        public static bool CreateConfiguration(string catalogue, string server, string database, string username, string password)
        {
            try
            {
                // Build connection string
                var builder = new SqlConnectionStringBuilder
                {
                    DataSource = server,
                    InitialCatalog = database,
                    UserID = username,
                    Password = password,
                    Encrypt = true,
                    TrustServerCertificate = false
                };

                // Create encrypted config file using the naming convention expected by SqlControllerBase
                string configFileName = $"BecaAzureSQL_{catalogue}.config";
                SecureConfigManager.CreateEncryptedConfig(builder.ConnectionString, configFileName);

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to create configuration: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Creates configuration for the default DevaBot catalogue
        /// </summary>
        /// <param name="server">Database server</param>
        /// <param name="database">Database name</param>
        /// <param name="username">Database username</param>
        /// <param name="password">Database password</param>
        /// <returns>True if configuration was created successfully</returns>
        public static bool CreateDevaBotConfiguration(string server, string database, string username, string password)
        {
            return CreateConfiguration("DevaBot", server, database, username, password);
        }

        /// <summary>
        /// Validates that the secure configuration exists for the specified catalogue
        /// </summary>
        /// <param name="catalogue">Database catalogue name</param>
        /// <returns>True if configuration exists and is accessible</returns>
        public static bool ValidateConfiguration(string catalogue)
        {
            try
            {
                string configFileName = $"BecaAzureSQL_{catalogue}.config";
                return SecureConfigManager.ValidateConfiguration(configFileName, $"BecaAzureSQL_{catalogue}");
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Gets the secure connection string for the specified catalogue
        /// This method can be used independently of SqlControllerBase
        /// </summary>
        /// <param name="catalogue">Database catalogue name</param>
        /// <returns>Secure connection string or null if not found</returns>
        public static string GetConnectionString(string catalogue)
        {
            try
            {
                string configFileName = $"BecaAzureSQL_{catalogue}.config";
                return SecureConfigManager.GetConnectionString(configFileName, $"BecaAzureSQL_{catalogue}");
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Migrates from hardcoded connection string to secure configuration
        /// This helps transition existing SqlController implementations
        /// </summary>
        /// <param name="catalogue">Database catalogue name</param>
        /// <param name="currentConnectionString">The current hardcoded connection string</param>
        /// <returns>True if migration was successful</returns>
        public static bool MigrateFromHardcodedConnectionString(string catalogue, string currentConnectionString)
        {
            try
            {
                // Validate the connection string by parsing it
                var builder = new SqlConnectionStringBuilder(currentConnectionString);
                
                // Create secure configuration
                string configFileName = $"BecaAzureSQL_{catalogue}.config";
                SecureConfigManager.CreateEncryptedConfig(currentConnectionString, configFileName);

                // Validate the migration worked
                return ValidateConfiguration(catalogue);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Migration failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Creates a backup of existing configuration before making changes
        /// </summary>
        /// <param name="catalogue">Database catalogue name</param>
        /// <returns>Path to backup file or null if backup failed</returns>
        public static string CreateConfigurationBackup(string catalogue)
        {
            try
            {
                string configFileName = $"BecaAzureSQL_{catalogue}.config";
                string assemblyLocation = System.Reflection.Assembly.GetExecutingAssembly().Location;
                string assemblyDirectory = Path.GetDirectoryName(assemblyLocation);
                string configPath = Path.Combine(assemblyDirectory, configFileName);

                if (File.Exists(configPath))
                {
                    string backupPath = configPath + ".backup." + DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    File.Copy(configPath, backupPath);
                    return backupPath;
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Example method showing how to set up configuration for common scenarios
        /// </summary>
        public static void SetupCommonConfigurations()
        {
            Console.WriteLine("Setting up common SqlController configurations...");

            // Example: Setup for DevaBot (replace with actual credentials)
            bool devaBotSuccess = CreateDevaBotConfiguration(
                server: "devabotdbserver.database.windows.net",
                database: "DevaBot",
                username: "devabotadmin",
                password: "YourSecurePassword" // Replace with actual password
            );

            if (devaBotSuccess)
            {
                Console.WriteLine("✅ DevaBot configuration created successfully");
            }
            else
            {
                Console.WriteLine("❌ Failed to create DevaBot configuration");
            }

            // Add more configurations as needed for other catalogues
            // Example for other databases:
            /*
            bool otherDbSuccess = CreateConfiguration(
                catalogue: "OtherDatabase",
                server: "otherserver.database.windows.net",
                database: "OtherDatabase",
                username: "otheradmin",
                password: "OtherSecurePassword"
            );
            */
        }

        /// <summary>
        /// Demonstrates migration from the old hardcoded approach
        /// </summary>
        public static void DemonstrateHardcodedMigration()
        {
            Console.WriteLine("Demonstrating migration from hardcoded connection string...");

            // The old hardcoded connection string from SqlControllerBase
            string oldConnectionString = "Data Source=devabotdbserver.database.windows.net;Initial Catalog=DevaBot;User ID=devabotadmin;Password=*********;";

            // Migrate to secure configuration
            bool migrationSuccess = MigrateFromHardcodedConnectionString("DevaBot", oldConnectionString);

            if (migrationSuccess)
            {
                Console.WriteLine("✅ Migration completed successfully");
                Console.WriteLine("SqlControllerBase will now use secure configuration instead of hardcoded connection string");
            }
            else
            {
                Console.WriteLine("❌ Migration failed");
            }
        }
    }
}
