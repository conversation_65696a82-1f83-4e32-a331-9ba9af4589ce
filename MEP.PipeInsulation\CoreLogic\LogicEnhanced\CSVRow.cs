﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PipeInsulation.CoreLogic.LogicEnhanced
{
    public class CSVRow
    {
        public string PipeSystem { get; set; }           // Pipe system name
        public string DesignLevel { get; set; }          // Base, Enhanced, or None
        public string CNC { get; set; }                  // Commercial or Non-Commercial
        public double MinDia { get; set; }               // Minimum diameter
        public double MaxDia { get; set; }               // Maximum diameter
        public double InteriorElastometric { get; set; } // Thickness for Interior Elastometric
        public double InteriorPolyethylene { get; set; } // Thickness for Interior Polyethylene
        public double InteriorPhenolic { get; set; }     // Thickness for Interior Phenolic
        public double InteriorFibreglass { get; set; }   // Thickness for Interior Fibreglass
        public double ExteriorElastometric { get; set; } // Thickness for Exterior Elastometric
        public double ExteriorPolyethylene { get; set; } // Thickness for Exterior Polyethylene
        public double ExteriorPhenolic { get; set; }     // Thickness for Exterior Phenolic
        public double ExteriorFibreglass { get; set; }   // Thickness for Exterior Fibreglass
    }
}
