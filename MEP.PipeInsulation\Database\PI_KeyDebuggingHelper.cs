using System;
using System.Security.Cryptography;
using System.Text;

namespace MEP.PipeInsulation.Database
{
    /// <summary>
    /// Helper class to debug and understand the key generation process
    /// This shows exactly what's happening with the encryption keys
    /// </summary>
    public static class PI_KeyDebuggingHelper
    {
        /// <summary>
        /// Shows the complete key generation process step by step
        /// </summary>
        public static void ShowKeyGenerationProcess()
        {
            Console.WriteLine("🔍 KEY GENERATION DEBUGGING");
            Console.WriteLine(new string('=', 60));

            // Step 1: Show old hardcoded approach
            Console.WriteLine("\n📜 OLD HARDCODED APPROACH:");
            Console.WriteLine(new string('-', 30));
            
            string oldCompanyKey = "RKE1jyALPgtUHsh2UKFdcG1HoBOyCdA7";
            Console.WriteLine($"1️⃣ Raw company key: '{oldCompanyKey}'");
            
            string oldEncryptionKey = GenerateEncryptionKey(oldCompanyKey);
            Console.WriteLine($"2️⃣ SHA256 hash (Base64): '{oldEncryptionKey}'");
            Console.WriteLine($"3️⃣ This is what gets used for AES encryption");

            // Step 2: Show new algorithmic approach
            Console.WriteLine("\n🔧 NEW ALGORITHMIC APPROACH:");
            Console.WriteLine(new string('-', 30));
            
            string newCompanyKey = GenerateAlgorithmicKey();
            Console.WriteLine($"1️⃣ Raw company key: '{newCompanyKey}'");
            
            string newEncryptionKey = GenerateEncryptionKey(newCompanyKey);
            Console.WriteLine($"2️⃣ SHA256 hash (Base64): '{newEncryptionKey}'");
            Console.WriteLine($"3️⃣ This is what gets used for AES encryption");

            // Step 3: Compare
            Console.WriteLine("\n⚖️ COMPARISON:");
            Console.WriteLine(new string('-', 30));
            Console.WriteLine($"Raw keys are different: {oldCompanyKey != newCompanyKey}");
            Console.WriteLine($"Encryption keys are different: {oldEncryptionKey != newEncryptionKey}");
            Console.WriteLine($"This means: Existing encrypted files CANNOT be decrypted with new key!");

            // Step 4: Show what you're seeing
            Console.WriteLine("\n👁️ WHAT YOU'RE SEEING:");
            Console.WriteLine(new string('-', 30));
            Console.WriteLine($"When you call GetCompanyEncryptionKey(), you get: '{newEncryptionKey}'");
            Console.WriteLine($"This is NOT the raw company key, it's the SHA256 hash!");
            Console.WriteLine($"The raw algorithmic key is: '{newCompanyKey}'");
        }

        /// <summary>
        /// Shows which key the system is currently using
        /// </summary>
        public static void ShowCurrentKeyInUse()
        {
            Console.WriteLine("\n🎯 CURRENT KEY IN USE:");
            Console.WriteLine(new string('=', 40));

            try
            {
                // This will trigger the key generation and show debug output
                string currentEncryptionKey = PI_SecureConfigManager.GetConnectionString();
                Console.WriteLine("✅ Successfully got connection string");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error getting connection string: {ex.Message}");
            }
        }

        /// <summary>
        /// Tests both old and new key generation
        /// </summary>
        public static void TestBothKeyTypes()
        {
            Console.WriteLine("\n🧪 TESTING BOTH KEY TYPES:");
            Console.WriteLine(new string('=', 40));

            // Test old key
            Console.WriteLine("\n🔑 Testing OLD key:");
            string oldKey = "RKE1jyALPgtUHsh2UKFdcG1HoBOyCdA7";
            string oldEncryptionKey = GenerateEncryptionKey(oldKey);
            
            string testData = "Test connection string";
            string encryptedWithOld = EncryptString(testData, oldEncryptionKey);
            Console.WriteLine($"✅ Encrypted with old key: {encryptedWithOld.Substring(0, 20)}...");

            try
            {
                string decryptedWithOld = DecryptString(encryptedWithOld, oldEncryptionKey);
                Console.WriteLine($"✅ Decrypted with old key: {decryptedWithOld}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Failed to decrypt with old key: {ex.Message}");
            }

            // Test new key
            Console.WriteLine("\n🔑 Testing NEW key:");
            string newKey = GenerateAlgorithmicKey();
            string newEncryptionKey = GenerateEncryptionKey(newKey);
            
            string encryptedWithNew = EncryptString(testData, newEncryptionKey);
            Console.WriteLine($"✅ Encrypted with new key: {encryptedWithNew.Substring(0, 20)}...");

            try
            {
                string decryptedWithNew = DecryptString(encryptedWithNew, newEncryptionKey);
                Console.WriteLine($"✅ Decrypted with new key: {decryptedWithNew}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Failed to decrypt with new key: {ex.Message}");
            }

            // Test cross-compatibility
            Console.WriteLine("\n🔄 Testing CROSS-COMPATIBILITY:");
            try
            {
                string decryptedCross = DecryptString(encryptedWithOld, newEncryptionKey);
                Console.WriteLine($"❌ This should fail: {decryptedCross}");
            }
            catch
            {
                Console.WriteLine($"✅ Correctly failed: Cannot decrypt old-encrypted data with new key");
            }
        }

        /// <summary>
        /// Generates the algorithmic key (same as PI_SecureConfigManager)
        /// </summary>
        private static string GenerateAlgorithmicKey()
        {
            int[] seeds = { 2100, 2500, 2200, 1800, 2700, 2400, 2300, 2600, 2000, 1900, 
                           2800, 2150, 1950, 2350, 2050, 2450, 1850, 2750, 2250, 2550,
                           2650, 1750, 2850, 2950, 1650, 2175, 2375, 2575, 2775, 2975,
                           1575, 2125 };
            
            StringBuilder sb = new StringBuilder();
            
            for (int i = 0; i < seeds.Length; i++)
            {
                int seed = seeds[i];
                int transform1 = (seed / 25) ^ 42;
                int transform2 = (transform1 + i * 7) % 126;
                int transform3 = transform2 < 32 ? transform2 + 65 : transform2;
                
                if (transform3 > 126) transform3 = (transform3 % 95) + 32;
                if (transform3 < 32) transform3 += 32;
                
                sb.Append((char)transform3);
            }
            
            return sb.ToString();
        }

        /// <summary>
        /// Generates SHA256 encryption key from company key
        /// </summary>
        private static string GenerateEncryptionKey(string companyKey)
        {
            using (var sha256 = SHA256.Create())
            {
                byte[] hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(companyKey));
                return Convert.ToBase64String(hash);
            }
        }

        /// <summary>
        /// Simple encryption for testing
        /// </summary>
        private static string EncryptString(string plainText, string key)
        {
            byte[] keyBytes = Convert.FromBase64String(key);

            using (var aes = System.Security.Cryptography.Aes.Create())
            {
                aes.Key = keyBytes;
                aes.GenerateIV();

                using (var encryptor = aes.CreateEncryptor())
                using (var msEncrypt = new System.IO.MemoryStream())
                {
                    msEncrypt.Write(aes.IV, 0, aes.IV.Length);

                    using (var csEncrypt = new System.Security.Cryptography.CryptoStream(msEncrypt, encryptor, System.Security.Cryptography.CryptoStreamMode.Write))
                    using (var swEncrypt = new System.IO.StreamWriter(csEncrypt))
                    {
                        swEncrypt.Write(plainText);
                    }

                    return Convert.ToBase64String(msEncrypt.ToArray());
                }
            }
        }

        /// <summary>
        /// Simple decryption for testing
        /// </summary>
        private static string DecryptString(string cipherText, string key)
        {
            byte[] keyBytes = Convert.FromBase64String(key);
            byte[] cipherBytes = Convert.FromBase64String(cipherText);

            using (var aes = System.Security.Cryptography.Aes.Create())
            {
                aes.Key = keyBytes;

                byte[] iv = new byte[aes.BlockSize / 8];
                Array.Copy(cipherBytes, 0, iv, 0, iv.Length);
                aes.IV = iv;

                using (var decryptor = aes.CreateDecryptor())
                using (var msDecrypt = new System.IO.MemoryStream(cipherBytes, iv.Length, cipherBytes.Length - iv.Length))
                using (var csDecrypt = new System.Security.Cryptography.CryptoStream(msDecrypt, decryptor, System.Security.Cryptography.CryptoStreamMode.Read))
                using (var srDecrypt = new System.IO.StreamReader(csDecrypt))
                {
                    return srDecrypt.ReadToEnd();
                }
            }
        }

        /// <summary>
        /// Quick test method you can call from your main code
        /// </summary>
        public static void QuickTest()
        {
            Console.WriteLine("🚀 QUICK KEY DEBUGGING TEST");
            Console.WriteLine(new string('=', 50));
            
            ShowKeyGenerationProcess();
            ShowCurrentKeyInUse();
        }
    }
}
