﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Plumbing;
using Autodesk.Revit.UI;
using Common.UI.Forms;
using MEP.PipeInsulation.CoreLogic;
using MEP.PipeInsulation.CoreLogic.LogicEnhanced;
using MEP.PipeInsulation.Database;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using DataTable = System.Data.DataTable;
using TaskDialog = Autodesk.Revit.UI.TaskDialog;

namespace MEP.PipeInsulation.Models
{
    public class PI_Data
    {
        private PI_DatabaseClient db;
        private DataSourceSystemTypeNames dataSourceSystemTypeNames;
        
        public View3D View3D { get; set; }

        public string BUID { get; private set; }
        public DataTable PI_LookupMaster { get; private set; }
        public DataTable PI_LookupCustom { get; set; }
        public DataTable PI_LookupCombined { get; set; }
        public string PI_SelectedDataSource { get; set; }

        public List<PI_PipingSystem> AllPipingSystems { get; set; }

        public PI_Data(Document doc, View3D view3D)
        {
            View3D = view3D;

            GetBUID(doc);

            SetMasterAndCustomLookup();

            PI_LookupCombined = CombineDataTables(PI_LookupMaster, PI_LookupCustom);

            dataSourceSystemTypeNames = GetDataSourceSystemTypeNames(PI_LookupCombined);

            AllPipingSystems = GetPipingSystems(doc, PI_LookupMaster, View3D);

            PI_SelectedDataSource = GetPI_SelectedDataSource(doc);
        }

        private List<PI_PipingSystem> GetPipingSystems(Document doc, DataTable dataTable, View3D view3D)
        {
            var pipeSystems = new FilteredElementCollector(doc)
                .OfCategory(BuiltInCategory.OST_PipingSystem)
                .WhereElementIsNotElementType()
                .Cast<PipingSystem>().OrderBy(p => p.Name);

            var pI_PipingSystems = new List<PI_PipingSystem>();

            int nCount = pipeSystems.Count();
            string progressMessage = "{0} of " + nCount.ToString() + " pipe systems processed...";
            string caption = $"Preparing systems.";
            using (BecaProgressForm pf = new BecaProgressForm(caption, progressMessage, nCount))
            {
                foreach (var ps in pipeSystems)
                {
                    pI_PipingSystems.Add(new PI_PipingSystem(doc, dataTable, ps, view3D, dataSourceSystemTypeNames));
                    
                    pf.Increment();
                }
            }

            return pI_PipingSystems;
        }

        private DataSourceSystemTypeNames GetDataSourceSystemTypeNames(DataTable dataTable)
        {
            return new DataSourceSystemTypeNames
            {
                NZAUBaseTypeNames = dataTable.AsEnumerable()
                                    .Where(
                                            row => row.Field<string>("Country") == PI_SourceNames.NZAU &&
                                                   (row.Field<string>("DesignLevel") != PI_DesignLevelNames.Enhanced)
                                          )
                                    .Select(row => row.Field<string>("System"))
                                    .Where(value => !string.IsNullOrEmpty(value))
                                    .Distinct()
                                    .OrderBy(name => name)
                                    .ToList(),
                NZAUEnhancedTypeNames = dataTable.AsEnumerable()
                                        .Where(
                                                row => row.Field<string>("Country") == PI_SourceNames.NZAU &&
                                                       (row.Field<string>("DesignLevel") == PI_DesignLevelNames.Enhanced)
                                              )
                                        .Select(row => row.Field<string>("System"))
                                        .Where(value => !string.IsNullOrEmpty(value))
                                        .Distinct()
                                        .OrderBy(name => name)
                                        .ToList(),
                SGTypeNames = dataTable.AsEnumerable()
                              .Where(row => row.Field<string>("Country") == PI_SourceNames.SG)
                              .Select(row => row.Field<string>("System"))
                              .Where(value => !string.IsNullOrEmpty(value))
                              .Distinct()
                              .OrderBy(name => name)
                              .ToList(),
                CustomTypeNames = dataTable.AsEnumerable()
                                  .Where(row => row.Field<string>("Country") == PI_SourceNames.SG)
                                  .Select(row => row.Field<string>("System"))
                                  .Where(value => !string.IsNullOrEmpty(value))
                                  .Distinct()
                                  .OrderBy(name => name)
                                  .ToList()
            };
        }

        private void SetMasterAndCustomLookup()
        {
            db = new PI_DatabaseClient();
            try
            {
                db.Connect();
                PI_LookupMaster = db.GetAllRowsFromMasterTable();
                PI_LookupCustom = db.GetAllRowsFromCustomTable(BUID, PI_SourceNames.NZAU);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to retrieve rows: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                db.Close();
            }
        }

        private void GetBUID(Document doc)
        {
            // Attempt to retrieve BUID from the Project Information parameter
            var buidParameter = doc.ProjectInformation.LookupParameter(PI_DefaultSettings.ProjectInfoParameterName_DatabaseLocation);

            if (buidParameter == null)
            {
                ShowError("'Beca_Pipe_Insulation_Database_Location' parameter is missing.\nPlease check the project information.");
                return;
            }

            if (buidParameter.IsReadOnly)
            {
                ShowError("The 'Beca_Pipe_Insulation_Database_Location' parameter is read-only.\nPlease check the project information.");
                return;
            }

            var buidInParameter = buidParameter.AsValueString();
            if (string.IsNullOrEmpty(buidInParameter))
            {
                string modelName = doc.Title;
                string centralPath = doc.PathName;

                if (string.IsNullOrEmpty(centralPath))
                {
                    ShowError("Failed to obtain the model path to create BUID.");
                    return;
                }

                BUID = GenerateHash(centralPath, modelName);
                using (var tr = new Transaction(doc, "Set BUID parameter"))
                {
                    try
                    {
                        tr.Start();
                        buidParameter.Set(BUID);
                        tr.Commit();
                    }
                    catch (Exception e)
                    {
                        MessageBox.Show(e.Message, "Setting parameter error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        tr.RollBack();
                    }
                }
            }
            else
            {
                BUID = buidInParameter;
            }

            if (string.IsNullOrEmpty(BUID))
            {
                ShowError("Cannot obtain BUID.\nCustom database will not work this time.");
            }
        }

        private void ShowError(string message)
        {
            TaskDialog.Show("Error", message);
        }

        public static string GenerateHash(params string[] inputs)
        {
            // Combine all strings
            string combined = String.Join("", inputs);
            // Convert the input string to a byte array and compute the hash.
            using (MD5 md5Hash = MD5.Create())
            {
                byte[] bytes = md5Hash.ComputeHash(Encoding.UTF8.GetBytes(combined));
                // Convert byte array to a string
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < bytes.Length; i++)
                {
                    builder.Append(bytes[i].ToString("x2"));
                }
                return builder.ToString();
            }
        }

        public DataTable CombineDataTables(DataTable dt1, DataTable dt2)
        {
            // Check if both DataTables have the same schema
            if (!dt1.Columns.Cast<DataColumn>().Select(c => c.ColumnName).SequenceEqual(dt2.Columns.Cast<DataColumn>().Select(c => c.ColumnName)))
            {
                throw new ArgumentException("The schemas of the two DataTables do not match.");
            }

            // Create a new DataTable to hold combined data
            DataTable combinedTable = dt1.Clone(); // Clone the schema of dt1

            // Import rows from the first DataTable
            foreach (DataRow row in dt1.Rows)
            {
                combinedTable.ImportRow(row);
            }

            // Import rows from the second DataTable
            foreach (DataRow row in dt2.Rows)
            {
                combinedTable.ImportRow(row);
            }

            return combinedTable;
        }

        private string GetPI_SelectedDataSource(Document doc)
        {
            string selectedDataSource = PI_SelectedDataSourceStorage.GetSelectedDataSourceString(doc);
            return !string.IsNullOrEmpty(selectedDataSource) ? selectedDataSource : PI_DataSource.NZ_AU_Base.ToString();
        }
    
    }

    public class DataSourceSystemTypeNames
    {
        public List<string> NZAUBaseTypeNames { get; set; }
        public List<string> NZAUEnhancedTypeNames { get; set; }
        public List<string> SGTypeNames { get; set; }
        public List<string> CustomTypeNames { get; set; }
    }
}
