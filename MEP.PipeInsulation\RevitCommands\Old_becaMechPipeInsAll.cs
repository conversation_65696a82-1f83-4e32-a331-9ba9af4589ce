﻿using Autodesk.Revit.ApplicationServices;
using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Plumbing;
using Autodesk.Revit.UI;
using MEP.PipeInsulation.CoreLogic;
using MEP.PipeInsulation.UI.Forms;
using BecaTransactionsNamesManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using BecaCommand;
using BecaRevitUtilities;
using Common.UI.Forms;
using TaskDialog = Autodesk.Revit.UI.TaskDialog;
using TaskDialogIcon = Autodesk.Revit.UI.TaskDialogIcon;
using Application = Autodesk.Revit.ApplicationServices.Application;

namespace MEP.PipeInsulation.RevitCommands
{
    [Autodesk.Revit.Attributes.Transaction(Autodesk.Revit.Attributes.TransactionMode.Manual)]
    class Old_becaMechPipeInsAll : BecaBaseCommand
    {
        public override Result ExecuteBecaCommand(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            UIApplication rqApp = commandData.Application;
            Autodesk.Revit.DB.Document rqDoc = rqApp.ActiveUIDocument.Document;
            Transaction rqTrans = new Transaction(rqDoc);
            Application rvtApp = rqApp.Application;
            UIDocument uidoc = rqApp.ActiveUIDocument;
            Document doc = uidoc.Document;

           
            _taskLogger.PreTaskStart();

            FrmbecaMechPipeInsAll MechPipeInsAll = new FrmbecaMechPipeInsAll();
            MechPipeInsAll.ShowDialog();
            if (System.Windows.Forms.DialogResult.Cancel == MechPipeInsAll.DialogResult)
            {
                return Autodesk.Revit.UI.Result.Cancelled;
            }

            //Get Pipe Insulation type element id (the first type element?)
            FilteredElementCollector rqInsulationTypesCol = new FilteredElementCollector(rqDoc).OfClass(typeof(PipeInsulationType));
            ICollection<Element> rqInsulationTypes = rqInsulationTypesCol.ToElements();
            //Terminate when the project doesn't have lining type setup
            if (0 == rqInsulationTypes.Count())
            {
                TaskDialog.Show("Beca Mechanical Tools", "Your project dosen't have correct MEP setup!");
                return Autodesk.Revit.UI.Result.Failed;
            }
            ElementId rqInsulationTypeID = rqInsulationTypes.ElementAt(0).Id;

            //Get all pipe insulation in the model
            FilteredElementCollector rqPipeInsulationCol = new FilteredElementCollector(rqDoc).OfClass(typeof(Autodesk.Revit.DB.Plumbing.PipeInsulation));
            ICollection<Element> rqPipeInsulations = rqPipeInsulationCol.ToElements();

            //Gettting All Pipes in the document
            FilteredElementCollector rqColPipes = new FilteredElementCollector(rqDoc).OfCategory(BuiltInCategory.OST_PipeCurves);
            ICollection<Element> rqPipes_Fittings = rqColPipes.ToElements();

            //Gettting All Pipe Fittings in the document, this may include preloaded pipe fitting families not being placed in the model
            FilteredElementCollector rqColPipeFittings = new FilteredElementCollector(rqDoc).OfCategory(BuiltInCategory.OST_PipeFitting);
            ICollection<Element> rqFittings = rqColPipeFittings.ToElements();

            //Merge all the "rqFittings" collection into the main collection "rqPipes_Fittings"
            foreach (Element elem in rqFittings)
            {
                rqPipes_Fittings.Add(elem);
            }

            //Start to process all the fittings in the collection "Fittings"
            Int32 rqCount = 0;
            double rqPipeInsThickness = 0;
            double rqPipeDia = 0;
            string rqPipeSysAbbr = "";
            string rqStrSysName = "";
            var nameGroup = new List<string>();

            /* Option 1 iterate through each element and delete before placement
            int rqI = 0;
            rqTrans.Start("Delete All Existing Insulations");

            foreach (Element rqIns in rqPipeInsulations)
            {
                rqDoc.Delete(rqIns.Id);
                rqI++;
            }
            rqTrans.Commit();
            //TaskDialog.Show("Revit", "Total Insulation Deleted: " + rqI);
            */

            //Start to processing all the pipes and fittings in the collection "rqPipes_Fittings"
            TaskDialog rqTD = new TaskDialog("PIPE INSULATION - All Pipes and Fittings");
            rqTD.Id = "ID_TD_AllPipes_Fittings";
            rqTD.MainIcon = TaskDialogIcon.TaskDialogIconWarning;
            rqTD.AllowCancellation = true;
            rqTD.MainInstruction = $"Standby! This process will look at {rqPipes_Fittings.Count} elements! Have you saved your project first?";
            rqTD.MainContent = "Also, be mindful that the new insullation created will be put on your current 'Active Workset'";
            rqTD.FooterText = "PIPE INSULATION";
            rqTD.CommonButtons = TaskDialogCommonButtons.Ok | TaskDialogCommonButtons.Cancel;
            rqTD.DefaultButton = TaskDialogResult.Ok;
            TaskDialogResult rqTDres = rqTD.Show();
            if (rqTDres == TaskDialogResult.Cancel)
            {
                return Result.Cancelled;
            }

            rqTrans.Start(BecaTransactionsNames.PipeInsulation_AddingAllInsulationsbasedonrules.GetHumanReadableString());

            int nCount = rqPipes_Fittings.Count;
            string progressMessage = "{0} of " + nCount.ToString() + " elements processed...";
            string caption = "Processing elements";
            using (var pf = new Common.UI.Forms.BecaProgressForm(caption, progressMessage, nCount))
            {
                foreach (Element rqPipeOrFittingElem in rqPipes_Fittings)
                {
                    //Find if the current selected element has Insulation, then get the Insulation element
                    Autodesk.Revit.DB.Plumbing.PipeInsulation rqExPipeIns = null;
                    foreach (Autodesk.Revit.DB.Plumbing.PipeInsulation rqIns in rqPipeInsulationCol)
                    {
                        if (rqIns.HostElementId == rqPipeOrFittingElem.Id)
                        {
                            rqExPipeIns = rqIns;
                            break; 
                        }
                    }

                    //Get Pipe Fitting Type element
                    Element rqPipeFamilyType = rqDoc.GetElement(rqPipeOrFittingElem.GetTypeId());

                    //Just in case it is a pipe fitting family not being placed as an instance
                    if (null != rqPipeFamilyType)
                    {
                        Parameter rqParaSysName = rqPipeOrFittingElem.get_Parameter(BuiltInParameter.RBS_SYSTEM_NAME_PARAM);

                        //Parameter rqParaInsThickness =rqPipeOrFittingElem.get_Parameter(BuiltInParameter.RBS_REFERENCE_INSULATION_THICKNESS);
                        //double rqExInsuThickness = becaMechDuctType1.rqMilimeterFromDouble(rqParaInsThickness.AsDouble());

                        if (null != rqParaSysName.AsString() && rqParaSysName.AsString().Count() >= 3)
                        {
                            rqStrSysName = rqParaSysName.AsString();
                            rqPipeSysAbbr = rqStrSysName.Substring(0, 3);
                            switch (rqPipeSysAbbr)
                            {
                                case "CHW":
                                case "HHW":
                                case "DC":
                                case "RF":
                                    if ("Pipes" == rqPipeOrFittingElem.Category.Name.ToString())
                                    {
                                        Pipe rqPipe = rqPipeOrFittingElem as Pipe;
                                        rqPipeDia = RevitUnitConvertor.MmToInternal(rqPipe.Diameter);
                                        /*
                                        PipingSystem rqPipingSys = rqPipe.MEPSystem as PipingSystem;
                                        rqPipeSysAbbr = rqPipingSys.Name.ToString().Substring(0, 3);
                                        */
                                    }
                                    else
                                    {
                                        Parameter rqParaDiameter = rqPipeOrFittingElem.LookupParameter("Nominal Diameter");
                                        if (null == rqParaDiameter)
                                        {
                                            rqParaDiameter = rqPipeOrFittingElem.LookupParameter("Nominal Diameter 1");
                                        }
                                        if (null != rqParaDiameter)
                                        {
                                            rqPipeDia = RevitUnitConvertor.MmToInternal(rqParaDiameter.AsDouble());
                                        }
                                    }
                                    rqPipeInsThickness = Helper.rqGetInsThickness(rqPipeSysAbbr, rqPipeDia);
                                    Helper.rqAddPipeIns(rqExPipeIns, rqDoc, rqPipeInsThickness, rqPipeOrFittingElem, rqInsulationTypeID, false);

                                    //Process the element base on the Beca Duct Type, delete lining if it is an insulation duct type(vice versa). Then add or edit the required insulation/lining.
                                    rqCount++;

                                    nameGroup.Add(rqPipeSysAbbr);
                                    pf.Increment();
                                    break;
                                default:
                                    nameGroup.Add(Helper.Ignored);
                                    pf.Increment();
                                    break;
                            }

                        }
                    }
                }
            }
            
            rqTrans.Commit();

            _taskLogger.PostTaskEnd("Pipe Insulation (all) completed.");

            // Show result, groupped and counted
            var groupedAndCounted = Helper.GroupAndCount(nameGroup);
            var summaryText = string.Join("\n", groupedAndCounted.Select(kvp => $"{kvp.Key}: {kvp.Value}"));
            BecaBaseMessageForm frmEnd = new BecaBaseMessageForm($"\n {rqCount} Pipe(s) and Fitting(s) Processed\n\n{summaryText}", "PIPE INSULATION RESULT");
            frmEnd.OkButtonText = "Finish";
            frmEnd.HideCancelButton = false;
            frmEnd.ShowDialog();

            return Autodesk.Revit.UI.Result.Succeeded;
        }

        public override string GetAddinAuthor()
        {
           return "Firza Utama";
        }

        public override string GetAddinName()
        {
            return AddinNames.PipeInsulationPlacerAll.Value;
        }

        public override string GetCommandSubName()
        {
            return string.Empty;
        }

    }
}
