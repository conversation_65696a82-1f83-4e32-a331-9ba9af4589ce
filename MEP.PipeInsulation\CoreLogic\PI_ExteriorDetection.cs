﻿using Autodesk.Revit.DB;
using BecaRevitUtilities;
using BecaRevitUtilities.Extensions;
using Common.UI.Forms;
using MEP.PipeInsulation.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MEP.PipeInsulation.CoreLogic
{
    public static class PI_ExteriorDetection
    {
        public static bool Run(List<PI_Element> selectedPipesAndFittings, View3D view3D)
        {
            var success = true;
            try
            {
                int nCount = selectedPipesAndFittings.Count;
                string progressMessage = "{0} of " + nCount.ToString() + " elements processed...";
                string caption = "Detecting exterior elements";
                using (var pf = new BecaProgressForm(caption, progressMessage, nCount))
                {
                    foreach (var e in selectedPipesAndFittings)
                    {
                        if ((BuiltInCategory)e.Element.Category.Id.IntegerValue() == BuiltInCategory.OST_PipeCurves)
                        {
                            e.IsInterior = CheckInteriorForPipeCurve(e.Element, view3D);
                        }
                        else
                        {
                            BoundingBoxXYZ box = e.Element.get_BoundingBox(view3D);
                            XYZ center = box.Min.Add(box.Max).Multiply(0.5);
                            e.IsInterior = CheckInterior(center, view3D);
                        }

                        pf.Increment();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                success = false;
            }
            return success;
        }

        private static bool CheckInteriorForPipeCurve(Element e, View3D view3D)
        {
            var startPoint = ((LocationCurve)e.Location).Curve.GetEndPoint(0);
            var endPoint = ((LocationCurve)e.Location).Curve.GetEndPoint(0);

            if (!CheckInterior(startPoint, view3D) || !CheckInterior(endPoint, view3D))
                return false;
            else
                return true;
        }

        private static bool CheckInterior(XYZ location, View3D view3D)
        {
            // Surrounding radius to look for
            double range = UnitConversionUtility.mm_Feet(100);

            // Six directions from linke element location
            var baseDirectionX = new XYZ(location.X + range, location.Y, location.Z) - location;
            var baseDirectionY = new XYZ(location.X, location.Y + range, location.Z) - location;
            var baseDirectionZ = new XYZ(location.X, location.Y, location.Z + range) - location;
            var baseDirectionMinX = new XYZ(location.X - range, location.Y, location.Z) - location;
            var baseDirectionMinY = new XYZ(location.X, location.Y - range, location.Z) - location;
            var baseDirectionMinZ = new XYZ(location.X, location.Y, location.Z - range) - location;


            var boundaries = new List<BuiltInCategory>() { BuiltInCategory.OST_Walls, BuiltInCategory.OST_Floors, BuiltInCategory.OST_Ceilings, BuiltInCategory.OST_Roofs };

            bool isInterior = true;

            bool foundWalls = true;
            bool foundCeilingOrRoof = true;
            bool foundFloor = true;

            foreach (var bic in boundaries)
            {
                ReferenceIntersector refIntersector = new ReferenceIntersector(new ElementCategoryFilter(bic), FindReferenceTarget.Face, view3D);
                refIntersector.FindReferencesInRevitLinks = true;

                switch (bic)
                {
                    case BuiltInCategory.OST_Walls:
                        foundWalls = IsBoundaryFound(refIntersector, location, baseDirectionX) &&
                                        IsBoundaryFound(refIntersector, location, baseDirectionMinX) &&
                                        IsBoundaryFound(refIntersector, location, baseDirectionY) &&
                                        IsBoundaryFound(refIntersector, location, baseDirectionMinY);
                        break;
                    case BuiltInCategory.OST_Floors:
                        foundFloor = IsBoundaryFound(refIntersector, location, baseDirectionMinZ);
                        break;
                    case BuiltInCategory.OST_Ceilings:
                        foundCeilingOrRoof = IsBoundaryFound(refIntersector, location, baseDirectionZ);
                        break;
                    case BuiltInCategory.OST_Roofs:
                        foundCeilingOrRoof = IsBoundaryFound(refIntersector, location, baseDirectionZ);
                        break;
                }
            }

            if (!foundWalls)
            {
                isInterior = false;
            }
            else if (!foundCeilingOrRoof || !foundFloor)
            {
                isInterior = false;
            }

            return isInterior;
        }

        private static bool IsBoundaryFound(ReferenceIntersector refIntersector, XYZ location, XYZ direction)
        {
            return refIntersector.FindNearest(location, direction) != null;
        }
    }
}
