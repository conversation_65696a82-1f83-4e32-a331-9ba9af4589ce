﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
	  <UseWindowsForms>true</UseWindowsForms>
	  <UseWPF>true</UseWPF>
	  <Configurations>Debug R20;Debug R21;Debug R22;Debug R23;Debug R24;Debug R25;Release R20;Release R21;Release R22;Release R23;Release R24;Release R25;Debug R26;Release R26</Configurations>
	  <UserSecretsId>480e25fd-8c4b-4980-8813-9c22c1f3493d</UserSecretsId>
   </PropertyGroup>

  <ItemGroup>
    <Compile Update="UI\Controls\SpaceDataSelector.cs" />
    <Compile Update="UI\Controls\TypesSelector_SizeFiltered.cs" />
    <Compile Update="UI\Controls\ParamValueFiltering.cs" />
    <Compile Update="UI\Controls\TypesSelector.cs" />
    <Compile Update="UI\Controls\ViewNameFiltering.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\BecaTransactionsNames\BecaTransactionsNamesManager.csproj" />
    <ProjectReference Include="..\Common.Extenstions\Common.Extenstions.csproj" />
    <ProjectReference Include="..\Common.UI\Common.UI.csproj" />
    <ProjectReference Include="..\Common.Utilities\Common.Utilities.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="GeometryMathUtilities\GeometricalAlgorithms\TSP\" />
    <Folder Include="UI\UiData\" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.2.2" />
    <PackageReference Include="Polly" Version="8.4.1" />
    <PackageReference Include="Polly.Core" Version="8.4.1" />
    <PackageReference Include="Microsoft.AspNetCore.SystemWebAdapters" Version="1.4.0" />
    <PackageReference Include="Microsoft.Bcl.AsyncInterfaces" Version="8.0.0" />
    <PackageReference Include="Microsoft.Bcl.TimeProvider" Version="8.0.1" />
    <PackageReference Include="System.Runtime.CompilerServices.Unsafe" Version="6.0.0" />
	  <PackageReference Include="Nice3point.Revit.Api.AdWindows" Version="$(RevitVersion).*" />
	  <PackageReference Include="Nice3point.Revit.Api.RevitAddInUtility" Version="$(RevitVersion).*" />
	  <PackageReference Include="Nice3point.Revit.Api.RevitAPIIFC" Version="$(RevitVersion).*" />
	  <PackageReference Include="Nice3point.Revit.Api.RevitAPIMacros" Version="$(RevitVersion).*" />
	  <PackageReference Include="Nice3point.Revit.Api.UIFramework" Version="$(RevitVersion).*" />
	  <PackageReference Include="Nice3point.Revit.Api.UIFrameworkServices" Version="$(RevitVersion).*" />
	  <PackageReference Include="Nice3point.Revit.Build.Tasks" Version="2.0.2" />
	  <PackageReference Include="Nice3point.Revit.Toolkit" Version="$(RevitVersion).*" />
	  <PackageReference Include="Nice3point.Revit.Extensions" Version="$(RevitVersion).*" />
	  <PackageReference Include="Nice3point.Revit.Api.RevitAPI" Version="$(RevitVersion).*" />
	  <PackageReference Include="Nice3point.Revit.Api.RevitAPIUI" Version="$(RevitVersion).*" />
  </ItemGroup>
</Project>