﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Plumbing;
using CommunityToolkit.Mvvm.ComponentModel;
using MEP.PipeInsulation.CoreLogic;
using MEP.PipeInsulation.CoreLogic.LogicEnhanced;
using MEP.PipeInsulation.Database;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DataTable = System.Data.DataTable;

namespace MEP.PipeInsulation.Models
{
    public partial class PI_PipingSystem : ObservableObject
    {
        public PipingSystem PipeSystem { get; set; }
        public string SystemTypeName { get; set; }
        public string Name { get; set; }
        public bool MatchSource { get; set; }

        [ObservableProperty]
        private bool isSelected = false;
        [ObservableProperty]
        private string selectedDataSource;
        [ObservableProperty]
        private string selectedCommercial;
        [ObservableProperty]
        private string selectedDesignLevel;
        [ObservableProperty]
        private string selectedMaterial;
        [ObservableProperty]
        private string selectedSystemTypeName;

        private DataTable _dataTable;
        private DataSourceSystemTypeNames _dataSourceSystemTypeNames;

        public ObservableCollection<PI_Element> PI_Elements { get; set; } = new ObservableCollection<PI_Element>();

        public List<string> MaterialOptions => PI_DatabaseFiltering.MaterialOptions;
        public List<string> SystemTypeNameOptions { get; set; } 

        public PI_PipingSystem(Document doc, DataTable dataTable, PipingSystem pipeSystem, View3D view3D, DataSourceSystemTypeNames dataSourceSystemTypeNames)
        {
            _dataTable = dataTable;
            _dataSourceSystemTypeNames = dataSourceSystemTypeNames;

            PipeSystem = pipeSystem;
            SystemTypeName = (doc.GetElement(PipeSystem.get_Parameter(BuiltInParameter.ELEM_FAMILY_PARAM)?.AsElementId()) as ElementType)?.Name ?? string.Empty;
            Name = pipeSystem.Name;

            MatchSource = SystemMatches(_dataTable);

            SelectedDataSource = PI_DatabaseFiltering.DataSourceOptions[0]; // Default to NZ+AU
            SelectedCommercial = PI_DatabaseFiltering.CommercialOptions[0]; // Default to Commercial
            SelectedDesignLevel = PI_DatabaseFiltering.DesignLevelOptions[0]; // Default to Base
            SelectedMaterial = PI_DatabaseFiltering.MaterialOptions[0]; // Default to "Worst-case"

            PI_Elements = GetPipesAndFittings(pipeSystem.Document, pipeSystem, view3D);

            if (MatchSource)
            {
                SystemTypeNameOptions = new List<string>() { SystemTypeName };
                SelectedSystemTypeName = SystemTypeName;
            }
            else
            {
                UpdateSystemStypeNameOptions();
            }
        }

        private bool SystemMatches(DataTable dt)
        {
            // Check if any DataTable row's "System" column matches the SystemTypeName (case-sensitive)
            return dt.AsEnumerable().Any(row => row[PI_DatabaseTableColumnNames.System].ToString() == SystemTypeName);
        }

        public static ObservableCollection<PI_Element> GetPipesAndFittings(Document doc, PipingSystem system, View3D view3D)
        {
            var pipesAndFittings = new ObservableCollection<PI_Element>();
            var elements = system.PipingNetwork;
            foreach (Element element in system.PipingNetwork)
            {
#if TargetYear2024 || TargetYear2025 || TargetYear2026
                if (element.Category.BuiltInCategory == BuiltInCategory.OST_PipeCurves || element.Category.BuiltInCategory == BuiltInCategory.OST_PipeFitting)
#else
                if ((BuiltInCategory)element.Category.Id.IntegerValue == BuiltInCategory.OST_PipeCurves || (BuiltInCategory)element.Category.Id.IntegerValue == BuiltInCategory.OST_PipeFitting)
#endif
                {
                    pipesAndFittings.Add(new PI_Element(doc, element, view3D));
                }
            }
            return pipesAndFittings;
        }

        public void UpdateSystemStypeNameOptions()
        {
            if (!MatchSource)
            {
                if (SelectedDataSource == PI_SourceNames.NZAU && SelectedDesignLevel == PI_DesignLevelNames.Base)
                {
                    SystemTypeNameOptions = _dataSourceSystemTypeNames.NZAUBaseTypeNames;
                }
                else if (SelectedDataSource == PI_SourceNames.NZAU && SelectedDesignLevel == PI_DesignLevelNames.Enhanced)
                {
                    SystemTypeNameOptions = _dataSourceSystemTypeNames.NZAUEnhancedTypeNames;
                }
                else if (SelectedDataSource == PI_SourceNames.SG)
                {
                    SystemTypeNameOptions = _dataSourceSystemTypeNames.SGTypeNames;
                }
                else if (SelectedDataSource == PI_SourceNames.Custom)
                {
                    SystemTypeNameOptions = _dataSourceSystemTypeNames.CustomTypeNames;
                }
            }
        }

    }
}
