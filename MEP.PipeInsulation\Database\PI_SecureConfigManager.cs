using System;
using System.Configuration;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace MEP.PipeInsulation.Database
{
    /// <summary>
    /// Secure configuration manager for handling encrypted database connection strings
    /// </summary>
    public static class PI_SecureConfigManager
    {
        private static readonly string ConfigFileName = "PipeInsulation.config";
        private static readonly string EncryptionKey = GetCompanyEncryptionKey();

        /// <summary>
        /// Gets the database connection string from encrypted configuration
        /// </summary>
        /// <returns>Decrypted connection string</returns>
        public static string GetConnectionString()
        {
            try
            {
                // First try to get from app.config (for development/testing)
                var connectionString = ConfigurationManager.ConnectionStrings["PipeInsulationDB"]?.ConnectionString;
                if (!string.IsNullOrEmpty(connectionString))
                {
                    return connectionString;
                }

                // Try to get from encrypted config file
                string configPath = GetConfigFilePath();
                if (File.Exists(configPath))
                {
                    string encryptedContent = File.ReadAllText(configPath);
                    return DecryptString(encryptedContent, EncryptionKey);
                }

                throw new ConfigurationErrorsException("No database configuration found. Please run configuration setup.");
            }
            catch (Exception ex)
            {
                throw new ConfigurationErrorsException($"Failed to retrieve database configuration: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Creates an encrypted configuration file with the connection string
        /// </summary>
        /// <param name="connectionString">The connection string to encrypt and store</param>
        public static void CreateEncryptedConfig(string connectionString)
        {
            try
            {
                string configPath = GetConfigFilePath();
                string encryptedContent = EncryptString(connectionString, EncryptionKey);

                // Ensure directory exists
                Directory.CreateDirectory(Path.GetDirectoryName(configPath));

                File.WriteAllText(configPath, encryptedContent);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to create encrypted configuration: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Gets the path to the configuration file
        /// </summary>
        private static string GetConfigFilePath()
        {
            string assemblyLocation = System.Reflection.Assembly.GetExecutingAssembly().Location;
            string assemblyDirectory = Path.GetDirectoryName(assemblyLocation);
            return Path.Combine(assemblyDirectory, ConfigFileName);
        }

        /// <summary>
        /// Gets the company-wide encryption key
        /// This key should be the same across all company deployments
        /// </summary>
        private static string GetCompanyEncryptionKey()
        {
            // Option 1: Embedded key (compile-time)
            // This key should be generated once and used across all deployments
            const string companyKey = "RKE1jyALPgtUHsh2UKFdcG1HoBOyCdA7";

            using (var sha256 = SHA256.Create())
            {
                byte[] hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(companyKey));
                return Convert.ToBase64String(hash);
            }
        }

        /// <summary>
        /// Encrypts a string using AES encryption
        /// </summary>
        private static string EncryptString(string plainText, string key)
        {
            byte[] keyBytes = Convert.FromBase64String(key);

            using (var aes = Aes.Create())
            {
                aes.Key = keyBytes;
                aes.GenerateIV();

                using (var encryptor = aes.CreateEncryptor())
                using (var msEncrypt = new MemoryStream())
                {
                    // Prepend IV to the encrypted data
                    msEncrypt.Write(aes.IV, 0, aes.IV.Length);

                    using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                    using (var swEncrypt = new StreamWriter(csEncrypt))
                    {
                        swEncrypt.Write(plainText);
                    }

                    return Convert.ToBase64String(msEncrypt.ToArray());
                }
            }
        }

        /// <summary>
        /// Decrypts a string using AES decryption
        /// </summary>
        private static string DecryptString(string cipherText, string key)
        {
            byte[] keyBytes = Convert.FromBase64String(key);
            byte[] cipherBytes = Convert.FromBase64String(cipherText);

            using (var aes = Aes.Create())
            {
                aes.Key = keyBytes;

                // Extract IV from the beginning of the cipher text
                byte[] iv = new byte[aes.BlockSize / 8];
                Array.Copy(cipherBytes, 0, iv, 0, iv.Length);
                aes.IV = iv;

                using (var decryptor = aes.CreateDecryptor())
                using (var msDecrypt = new MemoryStream(cipherBytes, iv.Length, cipherBytes.Length - iv.Length))
                using (var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                using (var srDecrypt = new StreamReader(csDecrypt))
                {
                    return srDecrypt.ReadToEnd();
                }
            }
        }

        /// <summary>
        /// Validates if the current configuration is accessible
        /// </summary>
        public static bool ValidateConfiguration()
        {
            try
            {
                string connectionString = GetConnectionString();
                return !string.IsNullOrEmpty(connectionString);
            }
            catch
            {
                return false;
            }
        }
    }
}
