using System;
using System.Configuration;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace MEP.PipeInsulation.Database
{
    /// <summary>
    /// Secure configuration manager for handling encrypted database connection strings
    /// </summary>
    public static class PI_SecureConfigManager
    {
        private static readonly string ConfigFileName = "PipeInsulation.config";
        private static readonly string EncryptionKey = GetCompanyEncryptionKey();

        /// <summary>
        /// Gets the database connection string from encrypted configuration
        /// </summary>
        /// <returns>Decrypted connection string</returns>
        public static string GetConnectionString()
        {
            try
            {
                // First try to get from app.config (for development/testing)
                var connectionString = ConfigurationManager.ConnectionStrings["PipeInsulationDB"]?.ConnectionString;
                if (!string.IsNullOrEmpty(connectionString))
                {
                    return connectionString;
                }

                // Try to get from encrypted config file
                string configPath = GetConfigFilePath();
                if (File.Exists(configPath))
                {
                    string encryptedContent = File.ReadAllText(configPath);
                    return DecryptString(encryptedContent, EncryptionKey);
                }

                throw new ConfigurationErrorsException("No database configuration found. Please run configuration setup.");
            }
            catch (Exception ex)
            {
                throw new ConfigurationErrorsException($"Failed to retrieve database configuration: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Creates an encrypted configuration file with the connection string
        /// </summary>
        /// <param name="connectionString">The connection string to encrypt and store</param>
        public static void CreateEncryptedConfig(string connectionString)
        {
            try
            {
                string configPath = GetConfigFilePath();
                string encryptedContent = EncryptString(connectionString, EncryptionKey);

                // Ensure directory exists
                Directory.CreateDirectory(Path.GetDirectoryName(configPath));

                File.WriteAllText(configPath, encryptedContent);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to create encrypted configuration: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Gets the path to the configuration file with multiple location fallbacks
        /// Priority: 1) Next to .dll (deployment), 2) Project directory (development), 3) User profile (secure)
        /// </summary>
        private static string GetConfigFilePath()
        {
            // Priority 1: Next to the executing assembly (.dll location) - Best for deployment
            string assemblyLocation = System.Reflection.Assembly.GetExecutingAssembly().Location;
            string assemblyDirectory = Path.GetDirectoryName(assemblyLocation);
            string assemblyConfigPath = Path.Combine(assemblyDirectory, ConfigFileName);

            if (File.Exists(assemblyConfigPath))
            {
                return assemblyConfigPath;
            }

            // Priority 2: Project directory (for development) - Look for project root
            string projectConfigPath = GetProjectDirectoryConfigPath();
            if (!string.IsNullOrEmpty(projectConfigPath) && File.Exists(projectConfigPath))
            {
                return projectConfigPath;
            }

            // Priority 3: User profile directory (secure fallback)
            string userProfilePath = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile);
            string userConfigPath = Path.Combine(userProfilePath, ".beca", "revit", ConfigFileName);
            if (File.Exists(userConfigPath))
            {
                return userConfigPath;
            }

            // Return the primary location (next to .dll) for creation if none exist
            return assemblyConfigPath;
        }

        /// <summary>
        /// Attempts to find the project directory for development scenarios
        /// </summary>
        private static string GetProjectDirectoryConfigPath()
        {
            try
            {
                string assemblyLocation = System.Reflection.Assembly.GetExecutingAssembly().Location;
                string currentDir = Path.GetDirectoryName(assemblyLocation);

                // Walk up the directory tree looking for project indicators
                while (currentDir != null && Path.GetPathRoot(currentDir) != currentDir)
                {
                    // Look for common project indicators
                    if (Directory.GetFiles(currentDir, "*.csproj").Length > 0 ||
                        Directory.GetFiles(currentDir, "*.sln").Length > 0 ||
                        Directory.Exists(Path.Combine(currentDir, "MEP.PipeInsulation")))
                    {
                        // Found project root, look for config in a secure subdirectory
                        string configDir = Path.Combine(currentDir, "Config", "Secure");
                        if (Directory.Exists(configDir))
                        {
                            return Path.Combine(configDir, ConfigFileName);
                        }
                    }
                    currentDir = Directory.GetParent(currentDir)?.FullName;
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Gets the company-wide encryption key with backward compatibility
        /// This maintains compatibility with existing encrypted config files
        /// </summary>
        private static string GetCompanyEncryptionKey()
        {
            // BACKWARD COMPATIBILITY: Check if we need to use the old hardcoded key
            // This ensures existing encrypted config files can still be decrypted
            if (ShouldUseOldKey())
            {
                Console.WriteLine("🔄 Using legacy hardcoded key for backward compatibility");
                return GetLegacyEncryptionKey();
            }

            // Use new algorithmic approach for new installations
            Console.WriteLine("🔑 Using new algorithmic key generation");
            string companyKey = GenerateCompanyKey();

            using (var sha256 = SHA256.Create())
            {
                byte[] hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(companyKey));
                return Convert.ToBase64String(hash);
            }
        }

        /// <summary>
        /// Determines whether to use the old hardcoded key for backward compatibility
        /// </summary>
        private static bool ShouldUseOldKey()
        {
            try
            {
                // Check if an existing config file exists that was encrypted with the old key
                string configPath = GetConfigFilePath();
                if (File.Exists(configPath))
                {
                    // Try to decrypt with the old key first
                    string encryptedContent = File.ReadAllText(configPath);
                    string oldKey = GetLegacyEncryptionKey();

                    try
                    {
                        DecryptString(encryptedContent, oldKey);
                        // If decryption succeeds, use old key
                        return true;
                    }
                    catch
                    {
                        // If decryption fails with old key, try new key
                        return false;
                    }
                }

                // No existing config file, use new key
                return false;
            }
            catch
            {
                // Default to new key if any error occurs
                return false;
            }
        }

        /// <summary>
        /// Gets the legacy encryption key (for backward compatibility only)
        /// This maintains the original hardcoded key for existing encrypted files
        /// </summary>
        private static string GetLegacyEncryptionKey()
        {
            // Original hardcoded key for backward compatibility
            const string legacyCompanyKey = "RKE1jyALPgtUHsh2UKFdcG1HoBOyCdA7";

            using (var sha256 = SHA256.Create())
            {
                byte[] hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(legacyCompanyKey));
                return Convert.ToBase64String(hash);
            }
        }

        /// <summary>
        /// Generates the company key algorithmically to avoid hardcoding
        /// This uses a deterministic algorithm that produces the same result every time
        /// but doesn't expose the actual key in plain text in the source code
        /// </summary>
        private static string GenerateCompanyKey()
        {
            // DEBUG: Add console output to help with debugging
            System.Diagnostics.Debug.WriteLine("🔑 GenerateCompanyKey() called - breakpoint should hit here!");
            Console.WriteLine("🔑 GenerateCompanyKey() called - generating new algorithmic key");

            // Enhanced keygen algorithm based on ForReferenceOnly.cs but more sophisticated
            // This generates a deterministic key that's harder to reverse engineer
            int[] seeds = { 2100, 2500, 2200, 1800, 2700, 2400, 2300, 2600, 2000, 1900,
                           2800, 2150, 1950, 2350, 2050, 2450, 1850, 2750, 2250, 2550,
                           2650, 1750, 2850, 2950, 1650, 2175, 2375, 2575, 2775, 2975,
                           1575, 2125 };

            StringBuilder sb = new StringBuilder();

            // Use multiple transformation layers to make reverse engineering harder
            for (int i = 0; i < seeds.Length; i++)
            {
                int seed = seeds[i];

                // Apply multiple mathematical transformations
                int transform1 = (seed / 25) ^ 42;
                int transform2 = (transform1 + i * 7) % 126;
                int transform3 = transform2 < 32 ? transform2 + 65 : transform2;

                // Ensure we get printable ASCII characters
                if (transform3 > 126) transform3 = (transform3 % 95) + 32;
                if (transform3 < 32) transform3 += 32;

                sb.Append((char)transform3);
            }

            string result = sb.ToString();

            // DEBUG: Show the generated key for comparison
            Console.WriteLine($"🔑 Generated algorithmic key: {result}");
            System.Diagnostics.Debug.WriteLine($"🔑 Generated algorithmic key: {result}");

            return result;
        }

        /// <summary>
        /// Encrypts a string using AES encryption
        /// </summary>
        private static string EncryptString(string plainText, string key)
        {
            byte[] keyBytes = Convert.FromBase64String(key);

            using (var aes = Aes.Create())
            {
                aes.Key = keyBytes;
                aes.GenerateIV();

                using (var encryptor = aes.CreateEncryptor())
                using (var msEncrypt = new MemoryStream())
                {
                    // Prepend IV to the encrypted data
                    msEncrypt.Write(aes.IV, 0, aes.IV.Length);

                    using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                    using (var swEncrypt = new StreamWriter(csEncrypt))
                    {
                        swEncrypt.Write(plainText);
                    }

                    return Convert.ToBase64String(msEncrypt.ToArray());
                }
            }
        }

        /// <summary>
        /// Decrypts a string using AES decryption
        /// </summary>
        private static string DecryptString(string cipherText, string key)
        {
            byte[] keyBytes = Convert.FromBase64String(key);
            byte[] cipherBytes = Convert.FromBase64String(cipherText);

            using (var aes = Aes.Create())
            {
                aes.Key = keyBytes;

                // Extract IV from the beginning of the cipher text
                byte[] iv = new byte[aes.BlockSize / 8];
                Array.Copy(cipherBytes, 0, iv, 0, iv.Length);
                aes.IV = iv;

                using (var decryptor = aes.CreateDecryptor())
                using (var msDecrypt = new MemoryStream(cipherBytes, iv.Length, cipherBytes.Length - iv.Length))
                using (var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                using (var srDecrypt = new StreamReader(csDecrypt))
                {
                    return srDecrypt.ReadToEnd();
                }
            }
        }

        /// <summary>
        /// Validates if the current configuration is accessible
        /// </summary>
        public static bool ValidateConfiguration()
        {
            try
            {
                string connectionString = GetConnectionString();
                return !string.IsNullOrEmpty(connectionString);
            }
            catch
            {
                return false;
            }
        }
    }
}
