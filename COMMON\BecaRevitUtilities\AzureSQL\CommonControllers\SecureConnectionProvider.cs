using System;
using Microsoft.Data.SqlClient;

namespace BecaRevitUtilities.AzureSQL.CommonControllers
{
    /// <summary>
    /// Provides secure database connections using encrypted configuration
    /// This class serves as a bridge between the secure configuration manager and the database connection
    /// </summary>
    public class SecureConnectionProvider
    {
        private readonly string _configFileName;
        private readonly string _connectionStringName;

        /// <summary>
        /// Initializes a new instance of SecureConnectionProvider
        /// </summary>
        /// <param name="configFileName">Optional custom config file name. If null, uses default.</param>
        /// <param name="connectionStringName">Connection string name in app.config for fallback</param>
        public SecureConnectionProvider(string configFileName = null, string connectionStringName = null)
        {
            _configFileName = configFileName;
            _connectionStringName = connectionStringName;
        }

        /// <summary>
        /// Gets the connection string from secure configuration
        /// </summary>
        /// <returns>Decrypted connection string</returns>
        public string GetConnectionString()
        {
            return SecureConfigManager.GetConnectionString(_configFileName, _connectionStringName);
        }

        /// <summary>
        /// Creates a new DatabaseConnection instance using secure configuration
        /// </summary>
        /// <returns>DatabaseConnection instance</returns>
        public DatabaseConnection CreateDatabaseConnection()
        {
            string connectionString = GetConnectionString();
            return new DatabaseConnection(connectionString);
        }

        /// <summary>
        /// Creates a new SqlConnection instance using secure configuration
        /// </summary>
        /// <returns>SqlConnection instance</returns>
        public SqlConnection CreateSqlConnection()
        {
            string connectionString = GetConnectionString();
            return new SqlConnection(connectionString);
        }

        /// <summary>
        /// Validates if the configuration is accessible
        /// </summary>
        /// <returns>True if configuration is valid and accessible</returns>
        public bool ValidateConfiguration()
        {
            return SecureConfigManager.ValidateConfiguration(_configFileName, _connectionStringName);
        }

        /// <summary>
        /// Creates encrypted configuration with provided connection details
        /// </summary>
        /// <param name="server">Database server</param>
        /// <param name="database">Database name</param>
        /// <param name="username">Username</param>
        /// <param name="password">Password</param>
        /// <returns>True if configuration was created successfully</returns>
        public bool CreateConfiguration(string server, string database, string username, string password)
        {
            return SecureConfigManager.CreateConfiguration(server, database, username, password, _configFileName);
        }

        /// <summary>
        /// Creates encrypted configuration with a pre-built connection string
        /// </summary>
        /// <param name="connectionString">The connection string to encrypt and store</param>
        public void CreateEncryptedConfig(string connectionString)
        {
            SecureConfigManager.CreateEncryptedConfig(connectionString, _configFileName);
        }
    }

    /// <summary>
    /// Factory class for creating secure connection providers for specific projects
    /// </summary>
    public static class SecureConnectionProviderFactory
    {
        /// <summary>
        /// Creates a secure connection provider for the Pipe Insulation project
        /// </summary>
        /// <returns>SecureConnectionProvider configured for Pipe Insulation</returns>
        public static SecureConnectionProvider CreatePipeInsulationProvider()
        {
            return new SecureConnectionProvider("PipeInsulation.config", "PipeInsulationDB");
        }

        /// <summary>
        /// Creates a secure connection provider with default settings
        /// </summary>
        /// <returns>SecureConnectionProvider with default configuration</returns>
        public static SecureConnectionProvider CreateDefaultProvider()
        {
            return new SecureConnectionProvider();
        }

        /// <summary>
        /// Creates a secure connection provider for a specific project
        /// </summary>
        /// <param name="projectName">Name of the project (used for config file naming)</param>
        /// <param name="connectionStringName">Connection string name in app.config</param>
        /// <returns>SecureConnectionProvider configured for the specified project</returns>
        public static SecureConnectionProvider CreateProjectProvider(string projectName, string connectionStringName = null)
        {
            string configFileName = $"{projectName}.config";
            return new SecureConnectionProvider(configFileName, connectionStringName);
        }
    }
}
