﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using Autodesk.Revit.UI.Selection;
using BecaActivityLogger.CoreLogic.Data;
//using BecaTelemetryHandler;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PipeInsulation.UI.ModelessRevitForm
{
    #region Request Handler

    /// <summary>
    ///   A class with methods to execute requests made by the dialog user.
    /// </summary>
    /// 
    public class RequestHandler : IExternalEventHandler
    {

        #region Fields

        BecaActivityLoggerData _logger;
        //TelemetryHandler _telemetry;
        List<Element> _selectedElemnets;
        // The value of the latest request made by the modeless form 
        Request _request = new Request();

        #endregion

        #region Properties

        /// <summary>
        /// A public property to access the current request value
        /// </summary>
        public Request Request
        {
            get { return _request; }
        }

        #endregion

        #region Constructors

        public RequestHandler(BecaActivityLoggerData logger)
        {
            _logger = logger;
            //_telemetry = telemetry;
        }

        #endregion

        #region Methods

        #region IExternalEventHandler Methods

        /// <summary>
        ///   A method to identify this External Event Handler
        /// </summary>
        public String GetName()
        {
            return "change to your name";
        }


        /// <summary>
        ///   The top method of the event handler.
        /// </summary>
        /// <remarks>
        ///   This is called by Revit after the corresponding
        ///   external event was raised (by the modeless form)
        ///   and Revit reached the time at which it could call
        ///   the event's handler (i.e. this object)
        /// </remarks>
        /// <returns>Status</returns>
        public void Execute(UIApplication uiapp)
        {
            try
            {
                switch (Request.Take())
                {
                    case RequestId.None:
                        {
                            return;  // no request at this time -> we can leave immediately
                        }

                    case RequestId.SelectElements:
                        {
                            ModelessPipeInsulationHandler.SelectElementsInModel(uiapp, _logger);
                            break;
                        }
                    case RequestId.Create3DView:
                        {
                            ModelessPipeInsulationHandler.Create3DViewHandler(uiapp, _logger);
                            break;
                        }
                    case RequestId.AddInsulations:
                        {
                            ModelessPipeInsulationHandler.AddInsulations(_logger);
                            break;
                        }
                    case RequestId.ChangeTypes:
                        {
                            ModelessPipeInsulationHandler.ChangeTypes(uiapp);
                            break;
                        }
                    case RequestId.RemoveAllInsulationsAndLinings:
                        {
                            ModelessPipeInsulationHandler.RemoveAllInsulations(_logger);
                            break;
                        }
                    default:
                        {
                            // some kind of a warning here should
                            // notify us about an unexpected request 
                            break;
                        }
                }
            }
            finally
            {
                ModelessPipeInsulationHandler.WakeFormUp();
            }

            return;
        }

        #endregion

        #region Logic Helpers

        private void ClearSelection(UIDocument activeUIDocument)
        {
            activeUIDocument.Selection.SetElementIds(new List<ElementId>());
        }
        private void NewElementsSelection(UIApplication uiapp)
        {

            int selectedELementsCount = uiapp.ActiveUIDocument.Selection.GetElementIds().Count;
            int StotredSelectedELementsCount = _selectedElemnets == null ? 0 : _selectedElemnets.Count;
            if (selectedELementsCount > 0)
            {
                var dialogResult = System.Windows.Forms.MessageBox.Show($"There are already {selectedELementsCount} selected elements. Do you wish to clear selection and select elements again?",
                    "Elements were selected", System.Windows.Forms.MessageBoxButtons.YesNo, System.Windows.Forms.MessageBoxIcon.Question);

                if (dialogResult != System.Windows.Forms.DialogResult.Yes)
                {
                    _selectedElemnets = uiapp.ActiveUIDocument.Selection.GetElementIds().Select(eleId => uiapp.ActiveUIDocument.Document.GetElement(eleId)).ToList();
                    ClearSelection(uiapp.ActiveUIDocument);
                    return;
                }
                ClearSelection(uiapp.ActiveUIDocument);
            }
            else if (StotredSelectedELementsCount > 0)
            {
                var dialogResult = System.Windows.Forms.MessageBox.Show($"There are already {StotredSelectedELementsCount} selected elements. Do you wish to clear selection and select elements again?",
                    "Elements were selected", System.Windows.Forms.MessageBoxButtons.YesNo, System.Windows.Forms.MessageBoxIcon.Question);

                if (dialogResult != System.Windows.Forms.DialogResult.Yes)
                {
                    return;
                }
            }

            IList<Reference> refs = uiapp.ActiveUIDocument.Selection.PickObjects(ObjectType.Element, "Please select elements");
            _selectedElemnets = refs.Select(eleID => uiapp.ActiveUIDocument.Document.GetElement(eleID)).ToList();


        }

        #endregion

        #endregion

    }

    #endregion
}
