﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PipeInsulation.UI.Views.ViewHandlers
{
    public enum RequestId : int
    {
        Close,
        SelectElementsFromRevit,
        SelectSystemFromRevit,
        Create3DView,
        DetectInteriorExterior,
        AddInsulations,
        RemoveAllInsulations,
        UpdatePI_SelectedDataSource
    }

    public class RequestPipeInsulationHandler
    {
        private int m_request = (int)RequestId.Close;

        public RequestId Take()
        {
            return (RequestId)Interlocked.Exchange(ref m_request, (int)RequestId.Close);
        }

        public void Make(RequestId request)
        {
            Interlocked.Exchange(ref m_request, (int)request);
        }
    }
}
