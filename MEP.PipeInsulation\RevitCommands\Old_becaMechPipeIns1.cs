﻿using Autodesk.Revit.ApplicationServices;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using Autodesk.Revit.UI.Selection;
using System;
using System.Collections.Generic;
using System.Linq;
using BecaRevitUtilities.SelectionFilters;
using System.Text;
using System.Threading.Tasks;
using Autodesk.Revit.DB.Plumbing;
using MEP.PipeInsulation.CoreLogic;
using MEP.PipeInsulation.UI.Forms;
using BecaTransactionsNamesManager;
using BecaCommand;
using BecaRevitUtilities;
using Common.UI.Forms;
using Application = Autodesk.Revit.ApplicationServices.Application;

namespace MEP.PipeInsulation.RevitCommands
{
    [Autodesk.Revit.Attributes.Transaction(Autodesk.Revit.Attributes.TransactionMode.Manual)]
    class Old_becaMechPipeIns1 : BecaBaseCommand
    {
        public override Result ExecuteBecaCommand(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            UIApplication uiapp = commandData.Application;
            UIDocument uidoc = uiapp.ActiveUIDocument;
            Document doc = uiapp.ActiveUIDocument.Document;
            Application app = uiapp.Application;
            
            _taskLogger.PreTaskStart();

            //Selected elements in model
            var selectedElements = GetSelectedPipesAndFittings(uidoc);

            if (selectedElements == null || selectedElements.Count == 0)
            {
                MessageBox.Show("Please select Pipes and Pipe Fittings");
                return Result.Cancelled;
            }

            FrmbecaMechPipeIns1 MechPipeIns1 = new FrmbecaMechPipeIns1(doc, selectedElements);
            MechPipeIns1.ShowDialog();
            if (DialogResult.Cancel == MechPipeIns1.DialogResult)
            {
                return Result.Cancelled;
            }
            _taskLogger.PostTaskEnd("Pipe Insulation (one) completed.");
         
            return Result.Succeeded;
        }

        public IList<Reference> GetSelectedPipesAndFittings(UIDocument uiDoc)
        {
            // Get the currently selected element IDs in the model
            ICollection<ElementId> selectedElementIds = uiDoc.Selection.GetElementIds();

            // Create a list to hold the filtered references
            IList<Reference> filteredReferences = new List<Reference>();

            // Create an instance of the custom selection filter
            OnlyPipeAndPipeFittingsSelectionFilter filter = new OnlyPipeAndPipeFittingsSelectionFilter();

            // Loop through the selected elements and filter by Pipe and Pipe Fittings
            foreach (ElementId id in selectedElementIds)
            {
                Element element = uiDoc.Document.GetElement(id);

                // Check if the element passes the filter
                if (filter.AllowElement(element))
                {
                    filteredReferences.Add(new Reference(element));
                }
            }

            // Return the list of filtered references
            return filteredReferences;
        }

        public override string GetAddinAuthor()
        {
           return "Firza Utama, Roy Qian";
        }

        public override string GetAddinName()
        {
            return AddinNames.PipeInsulationPlacerSelected.Value;
        }

        public override string GetCommandSubName()
        {
            return string.Empty;
        }
    }
}
