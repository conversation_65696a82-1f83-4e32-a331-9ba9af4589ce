﻿<Page
    x:Class="MEP.PipeInsulation.UI.Views.PI_StartPageNonCard"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:conv="clr-namespace:MEP.PipeInsulation.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.PipeInsulation.UI.Views"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="PI_StartPageNonCard"
    Width="Auto"
    Height="Auto"
    d:DesignHeight="1000"
    Background="white"
    mc:Ignorable="d">

    <!--  Window specific resources  -->
    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <!--  Window specific resources  -->
            <conv:BoolToSelectAllTextConverter x:Key="BoolToSelectAllTextConverter" />
        </ResourceDictionary>
    </Page.Resources>

    <Grid MinWidth="800" MinHeight="600">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="300" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height=".6*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  Project Name  -->
        <TextBlock
            Grid.ColumnSpan="2"
            Margin="10,10,0,0"
            Style="{StaticResource MaterialDesignHeadline5TextBlock}"
            Text="{Binding ProjectName}" />

        <!--  Revit Elements Selector  -->
        <GroupBox
            Grid.Row="1"
            Grid.ColumnSpan="2"
            Margin="10">
            <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
                <RadioButton
                    x:Name="rb_SelectSystemByName"
                    Checked="rb_SelectSystemByName_Checked"
                    Content="Select System by Name" />
                <RadioButton
                    x:Name="rb_SelectSystemFromRevit"
                    Margin="20,0,0,0"
                    Checked="rb_SelectSystemFromRevit_Checked"
                    Unchecked="rb_SelectSystemFromRevit_Unchecked" />
                <Button
                    x:Name="btn_SelectSystemFromRevit"
                    Margin="8,10,0,10"
                    Background="#12A8B2"
                    BorderBrush="#12A8B2"
                    Command="{Binding SelectSystemFromRevitCommand}"
                    Content="Select System from Revit"
                    Foreground="White"
                    IsEnabled="{Binding ElementName=rb_SelectSystemFromRevit, Path=IsChecked}" />
                <RadioButton
                    x:Name="rb_SelectElementsFromRevit"
                    Margin="20,0,0,0"
                    Checked="rb_SelectElementsFromRevit_Checked"
                    Command="{Binding SelectElementsFromRevitCommand}"
                    IsChecked="{Binding rb_SelectElementsFromRevit}"
                    Unchecked="rb_SelectElementsFromRevit_Unchecked" />
                <Button
                    x:Name="btn_SelectElementsFromRevit"
                    Margin="8,0,0,0"
                    Background="#12A8B2"
                    BorderBrush="#12A8B2"
                    Content="Select Elements from Revit"
                    Foreground="White"
                    IsEnabled="{Binding ElementName=rb_SelectElementsFromRevit, Path=IsChecked}" />
            </StackPanel>
        </GroupBox>

        <!--  System Selector  -->
        <GroupBox
            Grid.Row="2"
            Margin="10"
            Visibility="Visible">
            <Grid Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <TextBlock
                    Margin="0,0,0,10"
                    Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                    Text="Select Pipe Systems" />

                <TextBox
                    x:Name="tb_SearchSystem"
                    Grid.Row="1"
                    Width="326"
                    Margin="0,0,0,10"
                    HorizontalAlignment="Left"
                    materialDesign:HintAssist.Hint="Search system name..."
                    Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}" />

                <TreeView
                    x:Name="tv_Systems"
                    Grid.Row="2"
                    BorderBrush="LightGray"
                    BorderThickness=".5"
                    ItemsSource="{Binding TreeViewPipeSystems}"
                    VirtualizingPanel.IsVirtualizing="True"
                    VirtualizingPanel.ScrollUnit="Pixel"
                    VirtualizingPanel.VirtualizationMode="Recycling"
                    Visibility="Visible">
                    <TreeView.ItemTemplate>
                        <HierarchicalDataTemplate ItemsSource="{Binding Children}">
                            <CheckBox Content="{Binding Name}" IsChecked="{Binding IsSelected, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                        </HierarchicalDataTemplate>
                    </TreeView.ItemTemplate>
                    <TreeView.ItemContainerStyle>
                        <Style TargetType="TreeViewItem">
                            <Setter Property="IsExpanded" Value="True" />
                        </Style>
                    </TreeView.ItemContainerStyle>
                </TreeView>

                <StackPanel Grid.Row="3" Orientation="Horizontal">
                    <Button
                        x:Name="btn_SelectAllSystems"
                        Width="80"
                        Height="25"
                        Margin="0,10,10,0"
                        Padding="0,-7,0,-5"
                        Background="#12A8B2"
                        BorderBrush="#12A8B2"
                        Command="{Binding SelectAllFilteredSystemsCommand}"
                        Content="Select All"
                        FontSize="12"
                        FontWeight="Regular"
                        Foreground="White" />
                    <Button
                        x:Name="btn_ClearSelection"
                        Width="80"
                        Height="25"
                        Margin="10,10,10,0"
                        Padding="0,-7,0,-5"
                        Background="#12A8B2"
                        BorderBrush="#12A8B2"
                        Command="{Binding ClearSelectionCommand}"
                        Content="Clear All"
                        FontSize="12"
                        FontWeight="Regular"
                        Foreground="White" />
                </StackPanel>


            </Grid>
        </GroupBox>

        <!--  Elements DataGrid  -->
        <GroupBox
            Grid.Row="2"
            Grid.Column="1"
            Margin="10"
            Visibility="Visible">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>
                <TextBlock
                    Margin="13,0,0,10"
                    Style="{StaticResource MaterialDesignCaptionTextBlock}"
                    Text="{Binding SelectedElementsInfo}" />
                <DataGrid
                    Grid.Row="1"
                    Margin="10,0,10,10"
                    materialDesign:DataGridAssist.CellPadding="8,4"
                    AutoGenerateColumns="False"
                    GridLinesVisibility="All"
                    HorizontalScrollBarVisibility="Auto"
                    IsReadOnly="True"
                    ItemsSource="{Binding AllFilteredElements}"
                    VerticalScrollBarVisibility="Auto"
                    Visibility="Visible">
                    <DataGrid.Columns>
                        <DataGridTextColumn
                            Binding="{Binding Element.Id}"
                            Header="Element&#x0a;Id"
                            IsReadOnly="True" />
                        <DataGridTextColumn
                            Binding="{Binding SystemName}"
                            Header="System&#x0a;Name"
                            IsReadOnly="True" />
                        <DataGridTextColumn
                            Binding="{Binding SystemTypeName}"
                            Header="System&#x0a;Type"
                            IsReadOnly="True" />
                        <DataGridTextColumn
                            Binding="{Binding Element.Category.Name}"
                            Header="Category&#x0a;"
                            IsReadOnly="True" />
                        <DataGridTextColumn
                            Binding="{Binding IsInterior}"
                            Header="Is Interior&#x0a;"
                            IsReadOnly="True" />
                        <DataGridTextColumn
                            Binding="{Binding HasInsulation}"
                            Header="Has&#x0a;Insulation"
                            IsReadOnly="True" />
                        <DataGridTextColumn
                            Binding="{Binding Diameter}"
                            Header="Diameter&#x0a;"
                            IsReadOnly="True" />
                        <DataGridTemplateColumn Header="Revit&#x0a;Thickness" IsReadOnly="True">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding RevitThickness}">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Background" Value="Transparent" />
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsThicknessMatch}" Value="True">
                                                        <Setter Property="Background" Value="LightGreen" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding IsThicknessMatch}" Value="False">
                                                        <Setter Property="Background" Value="LightCoral" />
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Header="Calculated&#x0a;Thickness" IsReadOnly="True">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding CalculatedThickness}">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Background" Value="Transparent" />
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsThicknessMatch}" Value="True">
                                                        <Setter Property="Background" Value="LightGreen" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding IsThicknessMatch}" Value="False">
                                                        <Setter Property="Background" Value="LightCoral" />
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTextColumn
                            Binding="{Binding IsLocked}"
                            Header="Is Locked&#x0a;"
                            IsReadOnly="True" />
                        <DataGridTextColumn
                            Binding="{Binding Owner}"
                            Header="Owner&#x0a;"
                            IsReadOnly="True" />
                    </DataGrid.Columns>
                </DataGrid>
                <!--  Create 3D and Detect interior exterior buttons  -->
                <StackPanel
                    Grid.Row="2"
                    Grid.Column="1"
                    Margin="0,0,0,8"
                    HorizontalAlignment="Right"
                    Orientation="Horizontal">
                    <Button
                        Width="140"
                        Height="25"
                        Margin="0,0,30,0"
                        Padding="0,-7,0,-5"
                        Background="#8d0e84"
                        BorderBrush="#8d0e84"
                        Command="{Binding ResetAllInteriorCommand}"
                        Content="Reset all Interior"
                        FontSize="12"
                        FontWeight="Normal"
                        Foreground="White" />
                    <Button
                        Width="140"
                        Height="25"
                        Margin="0,0,30,0"
                        Padding="0,-7,0,-5"
                        Background="#8d0e84"
                        BorderBrush="#8d0e84"
                        Command="{Binding DetectInteriorExteriorCommand}"
                        Content="Detect Interior/Exterior"
                        FontSize="12"
                        FontWeight="Normal"
                        Foreground="White" />
                    <Button
                        Width="140"
                        Height="25"
                        Margin="0,0,20,0"
                        Padding="0,-7,0,-5"
                        HorizontalAlignment="Right"
                        Background="#8d0e84"
                        BorderBrush="#8d0e84"
                        Command="{Binding Create3DViewCommand}"
                        Content="Create 3D View"
                        FontSize="12"
                        FontWeight="Normal"
                        Foreground="White" />

                </StackPanel>
            </Grid>
        </GroupBox>

        <!--  Database Selector  -->
        <GroupBox Grid.Row="3" Margin="10">
            <Grid Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width=".25*" />
                    <ColumnDefinition Width=".65*" />
                </Grid.ColumnDefinitions>

                <!--  Data Source Selection  -->
                <TextBlock
                    Grid.ColumnSpan="2"
                    Margin="0,0,0,10"
                    Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                    Text="Select Database Source" />
                <RadioButton
                    x:Name="rb_IsNZAUBase"
                    Grid.Row="1"
                    Content="NZ/AU Base (Default)"
                    IsChecked="{Binding IsNZAUBase, Mode=TwoWay}" />
                <RadioButton
                    x:Name="rb_IsNZAUEnhanced"
                    Grid.Row="2"
                    Content="NZ/AU Enhanced"
                    IsChecked="{Binding IsNZAUEnhanced, Mode=TwoWay}" />
                <RadioButton
                    x:Name="rb_IsSG"
                    Grid.Row="3"
                    Content="SG"
                    IsChecked="{Binding IsSG, Mode=TwoWay}" />
                <RadioButton
                    x:Name="rb_IsCustom"
                    Grid.Row="4"
                    Content="Custom"
                    IsChecked="{Binding IsCustom, Mode=TwoWay}" />
                <Button
                    x:Name="btn_Info_ViewNZAU_Base"
                    Grid.Row="1"
                    Grid.Column="1"
                    Height="22"
                    Padding="0,-10,0,-10"
                    HorizontalAlignment="Center"
                    Background="Transparent"
                    BorderBrush="Transparent"
                    Click="btn_Info_ViewNZAU_Base_Click"
                    Content="{materialDesign:PackIcon Kind=Info,
                                                      Size=17}"
                    Foreground="#8d0e84"
                    IsEnabled="{Binding ElementName=rb_IsNZAUBase, Path=IsChecked}" />
                <Button
                    x:Name="btn_Info_ViewNZAU_Enhanced"
                    Grid.Row="2"
                    Grid.Column="1"
                    Height="22"
                    Padding="0,-10,0,-10"
                    HorizontalAlignment="Center"
                    Background="Transparent"
                    BorderBrush="Transparent"
                    Click="btn_Info_ViewNZAU_Enhanced_Click"
                    Content="{materialDesign:PackIcon Kind=Info,
                                                      Size=17}"
                    Foreground="#8d0e84"
                    IsEnabled="{Binding ElementName=rb_IsNZAUEnhanced, Path=IsChecked}" />
                <Button
                    x:Name="btn_ViewNZAU_Base"
                    Grid.Row="1"
                    Grid.Column="2"
                    Height="22"
                    Padding="0,-10,0,-10"
                    HorizontalAlignment="Left"
                    Background="Transparent"
                    BorderBrush="Transparent"
                    Click="btn_ViewNZAU_Base_Click"
                    Content="{materialDesign:PackIcon Kind=Eye,
                                                      Size=20}"
                    Foreground="#12A8B2"
                    IsEnabled="{Binding ElementName=rb_IsNZAUBase, Path=IsChecked}" />
                <Button
                    x:Name="btn_ViewNZAU_Enhanced"
                    Grid.Row="2"
                    Grid.Column="2"
                    Height="22"
                    Padding="0,-10,0,-10"
                    HorizontalAlignment="Left"
                    Background="Transparent"
                    BorderBrush="Transparent"
                    Click="btn_ViewNZAU_Enhanced_Click"
                    Content="{materialDesign:PackIcon Kind=Eye,
                                                      Size=20}"
                    Foreground="#12A8B2"
                    IsEnabled="{Binding ElementName=rb_IsNZAUEnhanced, Path=IsChecked}" />
                <Button
                    x:Name="btn_ViewSG"
                    Grid.Row="3"
                    Grid.Column="2"
                    Height="22"
                    Padding="0,-10,0,-10"
                    HorizontalAlignment="Left"
                    Background="Transparent"
                    BorderBrush="Transparent"
                    Click="btn_ViewSG_Click"
                    Content="{materialDesign:PackIcon Kind=Eye,
                                                      Size=20}"
                    Foreground="#12A8B2"
                    IsEnabled="{Binding ElementName=rb_IsSG, Path=IsChecked}" />
                <Button
                    x:Name="btn_EditCustom"
                    Grid.Row="4"
                    Grid.Column="2"
                    Height="22"
                    Padding="0,-10,0,-10"
                    HorizontalAlignment="Left"
                    Background="Transparent"
                    BorderBrush="Transparent"
                    Click="btn_EditCustom_Click"
                    Content="{materialDesign:PackIcon Kind=Pencil,
                                                      Size=20}"
                    Foreground="#12A8B2"
                    IsEnabled="{Binding ElementName=rb_IsCustom, Path=IsChecked}" />


                <!--  Commercial/Non-Commercial Selection  -->
                <StackPanel
                    Grid.Row="6"
                    Grid.ColumnSpan="3"
                    Margin="10,0,10,10"
                    HorizontalAlignment="Right"
                    Orientation="Horizontal">
                    <RadioButton Content="Commercial" IsChecked="{Binding IsCommercial, Mode=TwoWay}" />
                    <RadioButton
                        Margin="10,0,0,0"
                        Content="Non-Commercial"
                        IsChecked="{Binding IsNonCommercial, Mode=TwoWay}" />
                </StackPanel>

            </Grid>
        </GroupBox>

        <!--  Selected Pipe System  -->
        <GroupBox
            Grid.Row="3"
            Grid.Column="1"
            Margin="10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>
                <TextBlock
                    Margin="13,0,0,10"
                    Style="{StaticResource MaterialDesignCaptionTextBlock}"
                    Text="{Binding SelectedTypesInfo}" />
                <DataGrid
                    Grid.Row="1"
                    Margin="10,0,10,10"
                    AutoGenerateColumns="False"
                    ItemsSource="{Binding SelectedPipeSystems}"
                    SelectedItem="{Binding SelectedPipingSystem}">
                    <DataGrid.Columns>

                        <!--  Match Source  -->
                        <DataGridTemplateColumn Header="Match&#x0a;Source" IsReadOnly="True">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock HorizontalAlignment="Center" FontSize="16">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Text">
                                                    <Setter.Value>✔</Setter.Value>
                                                </Setter>
                                                <Setter Property="Foreground" Value="Green" />
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding MatchSource}" Value="False">
                                                        <Setter Property="Text" Value="❌" />
                                                        <Setter Property="Foreground" Value="Red" />
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!--  System Type  -->
                        <DataGridTextColumn
                            Binding="{Binding SystemTypeName}"
                            Header="System Type&#x0a;"
                            IsReadOnly="True" />

                        <!--  System Type In Database  -->
                        <DataGridTemplateColumn Header="System Type&#x0a;In Database">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <ComboBox
                                        Width="150"
                                        HorizontalAlignment="Left"
                                        ItemsSource="{Binding SystemTypeNameOptions}"
                                        SelectedItem="{Binding SystemTypeNameOptions[0]}"
                                        SelectedValue="{Binding SelectedSystemTypeName, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!--  Material ComboBox Column  -->
                        <DataGridTemplateColumn Header="Select Material&#x0a;">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <ComboBox
                                        Width="150"
                                        HorizontalAlignment="Left"
                                        ItemsSource="{Binding MaterialOptions}"
                                        SelectedValue="{Binding SelectedMaterial, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!--  CNC  -->
                        <DataGridTextColumn
                            Binding="{Binding SelectedCommercial}"
                            Header="C/NC&#x0a;"
                            IsReadOnly="True" />

                        <!--  Data Source  -->
                        <DataGridTextColumn
                            Binding="{Binding SelectedDataSource}"
                            Header="Database source&#x0a;"
                            IsReadOnly="True" />
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </GroupBox>

        <!--  Action Buttons  -->
        <GroupBox
            Grid.Row="4"
            Grid.ColumnSpan="2"
            Margin="10,0,10,10">
            <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
                <Button
                    x:Name="btn_Close"
                    Width="160"
                    Margin="10"
                    Background="#FFCE00"
                    BorderBrush="#FFCE00"
                    Click="btn_Close_Click"
                    Content="Close"
                    Foreground="Black" />
                <Button
                    x:Name="btn_RemoveInsulations"
                    Width="160"
                    Margin="10"
                    Background="#12A8B2"
                    BorderBrush="#12A8B2"
                    Command="{Binding RemoveAllInsulationsCommand}"
                    Content="Remove Insulations"
                    Foreground="White" />
                <Button
                    x:Name="btn_AddInsulations"
                    Width="160"
                    Margin="10"
                    Background="#12A8B2"
                    BorderBrush="#12A8B2"
                    Command="{Binding AddInsulationsCommand}"
                    Content="Add Insulations"
                    Foreground="White" />
            </StackPanel>
        </GroupBox>

    </Grid>
</Page>
