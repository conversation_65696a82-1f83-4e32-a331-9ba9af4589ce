﻿using CommunityToolkit.Mvvm.ComponentModel;
using MEP.PipeInsulation.Models;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PipeInsulation.UI.Views.ViewHandlers
{
    public class TreeViewPipeSystem : ObservableObject
    {
        public string Name { get; set; }

        private PI_PipingSystem _originalSystem;
        public PI_PipingSystem OriginalSystem
        {
            get => _originalSystem;
            set
            {
                if (_originalSystem != null)
                {
                    // Unsubscribe from old system
                    _originalSystem.PropertyChanged -= OriginalSystem_PropertyChanged;
                }

                _originalSystem = value;

                if (_originalSystem != null)
                {
                    // Subscribe to new system
                    _originalSystem.PropertyChanged += OriginalSystem_PropertyChanged;
                    // Initialize IsSelected from the original system
                    IsSelected = _originalSystem.IsSelected;
                }
            }
        }

        private void OriginalSystem_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(PI_PipingSystem.IsSelected))
            {
                // Update IsSelected when the original system changes
                IsSelected = OriginalSystem.IsSelected;
            }
        }

        public ObservableCollection<object> Children { get; } = new();

        private bool _isSelected;
        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    OnPropertyChanged();

                    // Update the original system
                    if (OriginalSystem != null)
                    {
                        OriginalSystem.IsSelected = value;
                    }
                }
            }
        }

        

    }
}
