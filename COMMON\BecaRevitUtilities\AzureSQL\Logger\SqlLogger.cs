﻿using BecaAzureSQL;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BecaAzureSQL
{
    public class SqlLogger : ILogger
    {
        private readonly StringBuilder _logBuilder = new StringBuilder();

        public string GetAllLogs()
        {
            return _logBuilder.ToString();
        }

        public void Log(string message)
        {
            _logBuilder.AppendLine(message);
        }
    }
}

