﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Plumbing;
using Autodesk.Revit.UI;
using Autodesk.Revit.UI.Selection;
using BecaActivityLogger.CoreLogic.Data;
using BecaRevitUtilities;
using BecaRevitUtilities.ElementUtilities;
using BecaRevitUtilities.Extensions;

//using BecaTelemetryHandler;
using Common.UI.Forms;
using MEP.PipeInsulation.CoreLogic;
using MEP.PipeInsulation.Models;
using MEP.PipeInsulation.UI.Forms;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using Color = System.Drawing.Color;

namespace MEP.PipeInsulation.UI.ModelessRevitForm
{
    enum ColumnNames
    {
        DuctTypeSetGuid,
        TypeMark,
        DuctShape,
        Insulation,
        InsulationThickness,
        Lining,
        LiningThickness,
        ApplyPolyester

    }

    public partial class ModelessPipeInsulation : BecaBaseForm
    {
        

        #region Fields

        private RequestHandler _handler;
        private ExternalEvent _externalEvent;

        BecaActivityLoggerData _logger;
        //TelemetryHandler _telemetry;

        public Document Doc;
        private UIDocument _uiDoc;
        
        public Element SelectedInsulationType;

        List<PI_Database> _databaseNZ;
        List<PI_Database> _databaseAU;
        List<PI_Database> _databaseSG;
        public List<List<PI_Database>> Databases;
        public List<PI_Database> SelectedDatabase;

        List<PipingSystem> _pipeSystems = new List<PipingSystem>();
        List<string> _typeMarks = new List<string>();

        private string _projectName;
        private string _searchbarPlaceholderText = "Search system name here ...";

        
        List<TreeNode> _selectedNodes;
        TreeNode _previousNode;
        Font _boldFont;
        Font _normalFont;

        PI_ProjectSettingsJsonHandler _jsonHandler;
        string _projectSettingsExportPathNZ;
        string _projectSettingsExportPathAU;
        string _projectSettingsExportPathSG;
        string _projectSettingsExportPathCustom;
        string _projectCustomDatabaseExportPath;

        #endregion

        #region Properties


        public UIApplication Uiapp;
        public View3D View3D;
        public List<PI_Element> SelectedPipesAndFittings;
        public Element SelectedElementInSystem;
        public List<string> SelectedSystemNames;
        public List<PI_Database> SelectedSettings;
        public string ViewName;
        public Dictionary<ElementId, ElementId> TypeMapping;
        public string SystemSizeToSet;
        public List<PI_Database> CustomDatabase;
        public List<PI_ProjectSettings> ProjectSettingsToRun;
        public string SelectedCountryReference;


        #endregion

        #region Constructors

        public ModelessPipeInsulation(ExternalEvent exEvent, RequestHandler handler, BecaActivityLoggerData taskLogger, UIApplication uiapp, View3D view3D)
        {
            InitializeComponent();

            _handler = handler;
            _externalEvent = exEvent;
            _logger = taskLogger;
            //_telemetry = tc;

            Uiapp = uiapp;
            _uiDoc = uiapp.ActiveUIDocument;
            Doc = _uiDoc.Document;
            View3D = view3D;
            SelectedInsulationType = GetInsulationType(Doc);

            _projectName = Doc.ProjectInformation.get_Parameter(BuiltInParameter.PROJECT_NAME).AsString();
            lbl_ProjectName.Text = _projectName;

            rb_NZ.Click += new EventHandler(RadioButtons_Click);
            rb_AU.Click += new EventHandler(RadioButtons_Click);
            rb_SG.Click += new EventHandler(RadioButtons_Click);
            rb_Custom.Click += new EventHandler(RadioButtons_Click);

            #region System List
            _selectedNodes = new List<TreeNode>();
            _boldFont = new Font("Microsoft Sans Serif", 8, FontStyle.Bold);
            _normalFont = new Font("Microsoft Sans Serif", 8, FontStyle.Regular);

            RefreshSystemList();

            _typeMarks = _pipeSystems.Select(x => x.get_Parameter(BuiltInParameter.ELEM_TYPE_PARAM).AsValueString())
                .Distinct().OrderBy(z => z).ToList();

            PopulateDefaultSettings(_typeMarks, true);

            cb_SystemSize.SelectedIndex = 1;

            Databases = PI_DefaultSettings.ReadExcelDatabaseFile(PI_DefaultSettings.DatabaseFolderPath);
            CustomDatabase = SelectedDatabase ?? Databases.FirstOrDefault(item => item.First().CountryReference == CountryReference.NZ.ToString());

            SelectedDatabase = Databases[(int)CountryReference.NZ]; 

            _jsonHandler = new PI_ProjectSettingsJsonHandler();
            _projectSettingsExportPathNZ = Path.Combine(PI_DefaultSettings.ProjectSettingsPath, _projectName, $"{CountryReference.NZ}.json");
            _projectSettingsExportPathAU = Path.Combine(PI_DefaultSettings.ProjectSettingsPath, _projectName, $"{CountryReference.AU}.json");
            _projectSettingsExportPathSG = Path.Combine(PI_DefaultSettings.ProjectSettingsPath, _projectName, $"{CountryReference.SG}.json");
            _projectSettingsExportPathCustom = Path.Combine(PI_DefaultSettings.ProjectSettingsPath, _projectName, $"{CountryReference.Custom}.json");
            _projectCustomDatabaseExportPath = Path.Combine(PI_DefaultSettings.ProjectSettingsPath, _projectName, $"{CountryReference.Custom} Database.json");

            Directory.CreateDirectory(Path.GetDirectoryName(_projectSettingsExportPathNZ));
            Directory.CreateDirectory(Path.GetDirectoryName(_projectSettingsExportPathAU));
            Directory.CreateDirectory(Path.GetDirectoryName(_projectSettingsExportPathSG));
            Directory.CreateDirectory(Path.GetDirectoryName(_projectSettingsExportPathCustom));
            #endregion
        }


        #endregion

        #region Methods

        #region Modeless Functionality

        /// <summary>
        ///   A private helper method to make a request
        ///   and put the dialog to sleep at the same time.
        /// </summary>
        /// <remarks>
        ///   It is expected that the process which executes the request 
        ///   (the Idling helper in this particular case) will also
        ///   wake the dialog up after finishing the execution.
        /// </remarks>
        ///
        private void MakeRequest(RequestId request)
        {
            _handler.Request.Make(request);
            _externalEvent.Raise();
            DozeOff();

        }

        /// <summary>
        ///   Control enabler / disabler 
        /// </summary>
        ///
        private void EnableCommands(bool status)
        {
            foreach (System.Windows.Forms.Control ctrl in this.Controls)
            {
                ctrl.Enabled = status;
            }
            if (!status)
            {
                this.btn_Cancel.Enabled = true;
            }
        }

        /// <summary>
        ///   DozeOff -> disable all controls (but the Exit button)
        /// </summary>
        /// 
        private void DozeOff()
        {
            EnableCommands(false);
        }

        /// <summary>
        ///   WakeUp -> enable all controls
        /// </summary>
        /// 
        public void WakeUp()
        {
            EnableCommands(true);
        }


        #endregion

        #region UI

        #region UI Helpers



        #endregion

        #region Button Clicks

        private Element GetInsulationType(Document doc)
        {
            return new FilteredElementCollector(doc).OfClass(typeof(PipeInsulationType)).ToElements().FirstOrDefault();
        }

        private void btn_Cancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void RadioButtons_Click(object sender, EventArgs e)
        {
            RefreshDGVSettings();
        }

        private void  RefreshDGVSettings()
        {
            if (rb_NZ.Checked)
            {
                // Populate dgv settings with NZ data
                PopulateDefaultSettings(_typeMarks, true);
                // Make read only all columns
                DisableDGV();
                btn_SaveChanges.Visible = false;
                SelectedCountryReference = rb_NZ.Text;
                SelectedDatabase = Databases[(int)CountryReference.NZ];
            }
            else if (rb_AU.Checked)
            {
                // Populate dgv settings with AU data
                PopulateDefaultSettings(_typeMarks, true);
                // Make read only all columns
                DisableDGV();
                btn_SaveChanges.Visible = false;
                SelectedCountryReference = rb_AU.Text;
                SelectedDatabase = Databases[(int)CountryReference.AU];
            }
            else if (rb_SG.Checked)
            {
                // Populate dgv settings with AU data
                PopulateDefaultSettings(_typeMarks, true);
                // Make read only all columns
                DisableDGV();
                btn_SaveChanges.Visible = false;
                SelectedCountryReference = rb_SG.Text;
                SelectedDatabase = Databases[(int)CountryReference.SG];
            }
            else if (rb_Custom.Checked)
            {
                if (!File.Exists(_projectSettingsExportPathCustom))
                {
                    PopulateDefaultSettings(_typeMarks, true);
                    EnableDGV();
                    btn_SaveChanges.Visible = true;
                    SelectedCountryReference = rb_Custom.Text;
                }
                else
                {
                    var insulationSettings = _jsonHandler.ImportFromJson(_projectSettingsExportPathCustom).First().InsulationSettings;

                    EnableDGV();
                    dgv_Settings.Rows.Clear();
                    dgv_Settings.RowHeadersVisible = false;
                    dgv_Settings.Columns[0].HeaderText = "\nTypeMark\n";
                    dgv_Settings.Columns[0].ReadOnly = true;

                    IsReadOnlyStyle(true);

                    for (int i = 0; i < insulationSettings.Count; i++)
                    {
                        dgv_Settings.Rows.Add(new string[] { "TypeMark", "Settings", "InsulationType" });
                        dgv_Settings.Rows[i].Cells[TypeMark.Index].Value = insulationSettings[i].TypeMark;
                        dgv_Settings.Rows[i].Cells[InsulationType.Index].Value = insulationSettings[i].InsulationType;

                    }
                    btn_SaveChanges.Visible = true;
                    SelectedCountryReference = rb_Custom.Text;
                }

            }
        }

        private void PopulateDefaultSettings(List<string> typeMarks, bool isReadOnly)
        {
            if (SelectedPipesAndFittings == null || SelectedPipesAndFittings.Count == 0)
                return;
           
            var selectedTypes = SelectedPipesAndFittings.Select(p => p.SystemType.Name).ToList();
            var filteredTypes = typeMarks.Where(t => selectedTypes.Contains(t)).ToList();

            dgv_Settings.RowHeadersVisible = false;
            dgv_Settings.Columns[0].HeaderText = "\nTypeMark\n";
            dgv_Settings.Rows.Clear();
            for (int i = 0; i < filteredTypes.Count; i++)
            {
                dgv_Settings.Rows.Add(new string[] { "TypeMark", "Settings", "InsulationType" });
                dgv_Settings.Rows[i].Cells[TypeMark.Index].Value = filteredTypes[i];
                dgv_Settings.Rows[i].Cells[InsulationType.Index].Value = "Worst Case (Beca Default)";

            }

            IsReadOnlyStyle(isReadOnly);

        }

        private void IsReadOnlyStyle(bool isReadOnly)
        {
            if (isReadOnly)
            {
                foreach (DataGridViewColumn col in dgv_Settings.Columns)
                {
                    dgv_Settings.Columns[col.Index].HeaderCell.Style.BackColor = Color.FromArgb(18, 168, 178);
                    dgv_Settings.Columns[col.Index].HeaderCell.Style.ForeColor = Color.White;
                    dgv_Settings.Columns[col.Index].DefaultCellStyle.ForeColor = Color.Gray;
                    dgv_Settings.Columns[col.Index].DefaultCellStyle.BackColor = Color.LightGray;
                }
            }
            else
            {
                foreach (DataGridViewColumn col in dgv_Settings.Columns)
                {
                    dgv_Settings.Columns[col.Index].HeaderCell.Style.BackColor = Color.FromArgb(18, 168, 178);
                    dgv_Settings.Columns[col.Index].HeaderCell.Style.ForeColor = Color.White;
                    dgv_Settings.Columns[col.Index].DefaultCellStyle.ForeColor = Color.Black;
                    dgv_Settings.Columns[col.Index].DefaultCellStyle.BackColor = Color.White;
                }
            }
        }

        private void PopulateDefaultCustomSettings()
        {
            try
            {
                for (int i = 0; i < _typeMarks.Count; i++)
                {
                    dgv_Settings.Rows.Add(new string[] { "TypeMark", "Settings", "InsulationType" });
                    dgv_Settings.Rows[i].Cells[TypeMark.Index].Value = _typeMarks[i];
                    dgv_Settings.Rows[i].Cells[InsulationType.Index].Value = "Worst Case (Beca Default)";

                }
            }
            catch (Exception ex)
            {

                throw;
            }

        }

        private void PopulateDefaultAUSettings()
        {
            //try
            //{
            //    SelectedSettings = PI_DefaultSettings.PopulateDefaultAUSettings();

            //    dgv_Settings.Rows.Clear();
            //    for (int i = 0; i < SelectedSettings.Count; i++)
            //    {
            //        dgv_Settings.Rows.Add(new string[] { "TypeMark", "RoundOval", "Rectangular", "RectangularInsulationLining", "Thickness" });
            //        dgv_Settings.Rows[i].Cells[0].Value = SelectedSettings[i].TypeMark;
            //        dgv_Settings.Rows[i].Cells[2].Value = SelectedSettings[i].Rectangular;
            //        dgv_Settings.Rows[i].Cells[3].Value = SelectedSettings[i].InsulationLining;

            //    }

            //    dgv_Settings.EnableHeadersVisualStyles = false;
            //    dgv_Settings.Columns[0].HeaderText = "\nTypeMark\n";
            //    foreach (DataGridViewColumn col in dgv_Settings.Columns)
            //    {
            //        dgv_Settings.Columns[col.Index].HeaderCell.Style.BackColor = Color.FromArgb(18, 168, 178);
            //        dgv_Settings.Columns[col.Index].HeaderCell.Style.ForeColor = Color.White;
            //        dgv_Settings.Columns[col.Index].DefaultCellStyle.BackColor = Color.LightGray;
            //        dgv_Settings.Columns[col.Index].DefaultCellStyle.ForeColor = Color.Gray;
            //        dgv_Settings.Columns[col.Index].DefaultCellStyle.SelectionBackColor = Color.LightGray;
            //        dgv_Settings.Columns[col.Index].DefaultCellStyle.SelectionForeColor = Color.Gray;
            //    }
            //}
            //catch (Exception)
            //{

            //    throw;
            //}
            
        }

        private void DisableDGV()
        {
            foreach (DataGridViewColumn col in dgv_Settings.Columns)
                col.ReadOnly = true;
        }

        private void EnableDGV()
        {
            foreach (DataGridViewColumn col in dgv_Settings.Columns)
                col.ReadOnly = false;
        }


        #endregion

        #endregion

        #endregion

        #region SYstem list control
        public void RefreshSystemList()
        {
            GetPipeSystemsAndPopulateTreeView();

            //PopulateSystemListBySystem();
        }

        private void GetPipeSystemsAndPopulateTreeView()
        {
            var pipeSystems = new FilteredElementCollector(Doc)
                .OfCategory(BuiltInCategory.OST_PipingSystem)
                .WhereElementIsNotElementType()
                .Cast<PipingSystem>().OrderBy(p => p.Name);
            foreach (var system in pipeSystems)
            {
                _pipeSystems.Add(system);
                tv_PipeSystemList.Nodes.Add(system.Name);
            }
                
        }


        //private void PopulateSystemListBySystem()
        //{
        //    tv_PipeSystemList.Nodes.Clear();
        //    foreach (var systemType in _pipeSystems)
        //    {
        //        tv_PipeSystemList.Nodes.Add(systemType.SystemType?.Name);
        //    }

        //}
        #endregion

        private void btn_ShowElements_Click(object sender, EventArgs e)
        {
            // Clear datasource
            dgv_SelectedELements.DataSource = null;

            // Get checked systems
            List<PipingSystem> checkedSystems = new List<PipingSystem>();
            List<string> missingSystem = new List<string>();
            foreach (TreeNode node in tv_PipeSystemList.Nodes)
            {
                if (node.Checked)
                {
                    var pipeSystem = _pipeSystems.Find(ps => ps.Name == node.Text);
                    if (pipeSystem != null)
                    {
                        checkedSystems.Add(pipeSystem);
                    }
                    else
                    {
                        MessageBox.Show($"Can't find system: {node.Text}");
                    }
                }
            }

            if (checkedSystems.Count > 0)
            {
                SelectedPipesAndFittings = new List<PI_Element>();
                SelectedSystemNames = new List<string>();

                // Loop selected systems
                foreach (var system in checkedSystems)
                {
                    try
                    {
                        var elements = system.PipingNetwork;
                        foreach (Element element in system.PipingNetwork)
                        {
                            if ((BuiltInCategory)element.Category.Id.IntegerValue() == BuiltInCategory.OST_PipeCurves || (BuiltInCategory)element.Category.Id.IntegerValue() == BuiltInCategory.OST_PipeFitting)
                                SelectedPipesAndFittings.Add(new PI_Element(Doc, element, View3D));
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show(ex.Message);
                    }

                    SelectedSystemNames.Add(system.Name);
                }

                if (SelectedPipesAndFittings.Count == 0)
                {
                    MessageBox.Show("No element can be found");
                    return;
                }

                PopulateSelectedElements(SelectedPipesAndFittings);

                lbl_ElementsCount.Text = $"Number of elements: {SelectedPipesAndFittings.Count}";

                btn_ShowElements.Visible = false;

                // Filter Type Marks to only show in settings the type from SelectedPipesAndFittings
                RefreshDGVSettings();
            }
            else
            {
                var sb = new StringBuilder();
                foreach (var system in missingSystem)
                {
                    sb.AppendLine(system);
                }
                MessageBox.Show($"Can't find system: \n{sb}");
            }
        }

        private void PopulateSelectedElements(List<PI_Element> pipesAndFittings)
        {
            var dt = new DataTable("SelectedElementsDT");
            dt.Columns.Add("ElementId");
            dt.Columns.Add("SystemName");
            dt.Columns.Add("Type");
            dt.Columns.Add("TypeName");
            dt.Columns.Add("Category");
            dt.Columns.Add("IsInterior");
            dt.Columns.Add("Insulation");
            dt.Columns.Add("LockedBy");

            foreach (var e in pipesAndFittings)
            {
                dt.Rows.Add(
                    e.Element?.Id,
                    e.SystemName,
                    e.SystemType?.Name,
                    e.SystemType?.FamilyName + " | " + e.Element?.Name, 
                    e.Element.Category?.Name,
                    e.IsInterior,
                    e.HasInsulation, 
                    LockedBy(e.Element));
            }

            dgv_SelectedELements.DataSource = dt;
            dgv_SelectedELements.Columns["ElementId"].HeaderText = "\nElementId\n";
            dgv_SelectedELements.Columns["ElementId"].ReadOnly = true;
            dgv_SelectedELements.Columns["SystemName"].ReadOnly = true;
            dgv_SelectedELements.Columns["Type"].ReadOnly = true;
            dgv_SelectedELements.Columns["TypeName"].ReadOnly = true;
            dgv_SelectedELements.Columns["Category"].ReadOnly = true;
            dgv_SelectedELements.Columns["IsInterior"].ReadOnly = true;
            dgv_SelectedELements.Columns["Insulation"].ReadOnly = true;
            dgv_SelectedELements.Columns["LockedBy"].ReadOnly = true;

            dgv_SelectedELements.EnableHeadersVisualStyles = false;
            foreach (DataGridViewColumn col in dgv_SelectedELements.Columns)
            {
                dgv_SelectedELements.Columns[col.Index].HeaderCell.Style.BackColor = Color.FromArgb(18, 168, 178);
                dgv_SelectedELements.Columns[col.Index].HeaderCell.Style.ForeColor = Color.White;
            }

            foreach (DataGridViewRow row in dgv_SelectedELements.Rows)
            {
                if (row.Cells["LockedBy"].Value.ToString() != "False")
                    row.Cells["LockedBy"].Style = new DataGridViewCellStyle() { BackColor = Color.Red };

                if (string.IsNullOrEmpty(row.Cells["Insulation"].Value.ToString()))
                    row.Cells["Insulation"].Style = new DataGridViewCellStyle() { BackColor = Color.Red };
            }

        }

        private string LockedBy(Element e)
        {
            var lockedBy = "False";
            if (e.IsLocked(Doc))
                lockedBy = e.ElementOwner(Doc);
            return lockedBy;
        }

        private void tv_PipeSystemList_AfterCheck(object sender, TreeViewEventArgs e)
        {
            //if (e.Node.Level == 0)
            //{
            //    if (e.Node.Checked)
            //    {

            //        ShowOrHideRefreshButton();
            //    }
            //    else
            //    {
            //        ShowOrHideRefreshButton();
            //    }

            //    return;
            //}

            if (e.Node.Checked)
            {
                foreach (var node in _selectedNodes)
                {
                    if (!node.Checked)
                    {
                        node.Checked = true;
                    }
                }

                // Clear datasource
                dgv_SelectedELements.DataSource = null;

                ShowOrHideRefreshButton();
            }
            else
            {
                foreach (var node in _selectedNodes)
                {
                    if (node.Checked)
                    {
                        node.Checked = false;
                    }
                }

                // Clear datasource
                dgv_SelectedELements.DataSource = null;

                ShowOrHideRefreshButton();

            }


            tv_PipeSystemList.SelectedNode = null;
        }

        private void ShowOrHideRefreshButton()
        {
            var stillChecked = false;
            foreach (TreeNode node in tv_PipeSystemList.Nodes)
            {
                if (node.Checked)
                {
                    stillChecked = true;
                    break;
                }
            }
                    

            if (!stillChecked)
                btn_ShowElements.Visible = false;
            else
                btn_ShowElements.Visible = true;

        }

        private void tv_PipeSystemList_BeforeSelect(object sender, TreeViewCancelEventArgs e)
        {
            var currentNode = e.Node;
            if (currentNode == null) return;
            currentNode.BackColor = tv_PipeSystemList.BackColor;
            currentNode.ForeColor = tv_PipeSystemList.ForeColor;

            bool control = (ModifierKeys == Keys.Control);
            bool shift = (ModifierKeys == Keys.Shift);

            if (control)
            {

                // the node clicked with control button pressed:
                // invert selection of the current node
                var addedNodes = new List<TreeNode>();
                var removedNodes = new List<TreeNode>();
                if (!_selectedNodes.Contains(currentNode))
                {
                    addedNodes.Add(currentNode);
                    _previousNode = currentNode;
                }
                else
                {
                    removedNodes.Add(currentNode);
                }
                changeSelection(addedNodes, removedNodes);

            }
            else if (shift && _previousNode != null)
            {
                if (currentNode.Parent == _previousNode.Parent)
                {
                    // the node clicked with shift button pressed:
                    // if current node and previously selected node
                    // belongs to the same parent,
                    // select range of nodes between these two
                    var addedNodes = new List<TreeNode>();
                    var removedNodes = new List<TreeNode>();
                    bool selection = false;
                    bool selectionEnd = false;

                    TreeNodeCollection nodes = null;
                    if (_previousNode.Parent == null)
                    {
                        nodes = tv_PipeSystemList.Nodes;
                    }
                    else
                    {
                        nodes = _previousNode.Parent.Nodes;
                    }
                    foreach (TreeNode n in nodes)
                    {
                        if (n.Index == 0)
                        {
                            addedNodes.Add(n);
                        }
                        if (n == currentNode || n == _previousNode)
                        {
                            if (selection)
                            {
                                selectionEnd = true;
                            }
                            if (!selection)
                            {
                                selection = true;
                            }
                        }
                        
                        if (selection && !_selectedNodes.Contains(n))
                        {
                            addedNodes.Add(n);
                        }
                        if (selectionEnd)
                        {
                            break;
                        }
                    }

                    if (addedNodes.Count > 0)
                    {
                        changeSelection(addedNodes, removedNodes);

                    }
                }
            }
            else
            {
                // single click:
                // remove all selected nodes
                // and add current node
                var addedNodes = new List<TreeNode>();
                var removedNodes = new List<TreeNode>();
                removedNodes.AddRange(_selectedNodes);
                if (removedNodes.Contains(currentNode))
                {
                    removedNodes.Remove(currentNode);
                }
                else
                {
                    addedNodes.Add(currentNode);
                }
                changeSelection(addedNodes, removedNodes);
                _previousNode = currentNode;
            }
            foreach (TreeNode n in tv_PipeSystemList.Nodes)
            {
                if(n.Checked)
                    n.Checked = false;
            }
        }

        protected void changeSelection(List<TreeNode> addedNodes, List<TreeNode> removedNodes)
        {
            if (addedNodes != null)
            {
                foreach (TreeNode n in addedNodes)
                {
                    n.BackColor = SystemColors.Highlight;
                    n.ForeColor = SystemColors.HighlightText;
                    _selectedNodes.Add(n);
                }
            }
            if (removedNodes != null)
            {
                foreach (TreeNode n in removedNodes)
                {
                    n.BackColor = tv_PipeSystemList.BackColor;
                    n.ForeColor = tv_PipeSystemList.ForeColor;
                    _selectedNodes.Remove(n);
                    //n.Checked = false;
                }
            }
        }

        private void dgv_Settings_DataError(object sender, DataGridViewDataErrorEventArgs e)
        {
            if (e.Exception.Message == "DataGridViewComboBoxCell value is not valid.")
            {
                object value = dgv_Settings[e.ColumnIndex, e.RowIndex].Value;
                if (!dgv_Settings.Columns[e.ColumnIndex].Name.Contains(value.ToString()))
                {
                    dgv_Settings.Columns[e.ColumnIndex].Name = value.ToString();
                    e.ThrowException = false;
                }
            }
        }

        private void btn_SaveChanges_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(SelectedCountryReference))
                    SelectedCountryReference = CountryReference.NZ.ToString();

                var insulationSettings = new List<PI_InsulationSettings>();

                for (int i = 0; i < dgv_Settings.Rows.Count - 1; i++)
                {
                    insulationSettings.Add(new PI_InsulationSettings()
                    {
                        TypeMark = dgv_Settings.Rows[i].Cells[TypeMark.Index].Value?.ToString(),
                        Settings = String.Empty,
                        InsulationType = dgv_Settings.Rows[i].Cells[InsulationType.Index].Value?.ToString()
                    });
                }
                var projectSettings = new PI_ProjectSettings()
                {
                    CountryReference = SelectedCountryReference,
                    SystemSize = cb_SystemSize.SelectedItem.ToString(),
                    InsulationSettings = insulationSettings
                };

                _jsonHandler.Settings = new List<PI_ProjectSettings>();
                _jsonHandler.Settings.Add(projectSettings);

                string exportPath = _projectSettingsExportPathNZ;
                if (SelectedCountryReference == CountryReference.NZ.ToString())
                {
                    exportPath = _projectSettingsExportPathNZ;
                }
                else if (SelectedCountryReference == CountryReference.AU.ToString())
                {
                    exportPath = _projectSettingsExportPathAU;
                }
                else if (SelectedCountryReference == CountryReference.SG.ToString())
                {
                    exportPath = _projectSettingsExportPathSG;
                }
                else if (SelectedCountryReference == CountryReference.Custom.ToString())
                {
                    exportPath = _projectSettingsExportPathCustom;
                }
                _jsonHandler.ExportToJson(exportPath);
                _jsonHandler.Settings = null;

                MessageBox.Show($"Save Successful:\n\nSettings have been saved to '{exportPath}'\n\nThe existing settings file for this project has been updated. ");
            }
            catch (Exception)
            {

            }
            
        }

        private void btn_Run_Click(object sender, EventArgs e)
        {
            if (SelectedPipesAndFittings == null || SelectedPipesAndFittings.Count == 0)
            {
                MessageBox.Show("No elements found, please check element list");
                this.DialogResult = DialogResult.None;
                return;
            }

            var insulatedCount = SelectedPipesAndFittings.Where(p => p.HasInsulation).Count();
            if (insulatedCount > 0)
            {
                MessageBox.Show($"{insulatedCount} out of {SelectedPipesAndFittings.Count} elements have insulation applied.\n\nPlease check and remove any existing insulation before adding new insulations.", "Insulated elements");
                return;
            }

            //DialogResult dr = MessageBox.Show("Do you want the tool to check for exterior pipes?\n\n(warning: This process will take time, depending on the number of elements.)\n\nClicking 'No' will apply all as interior",
            //    "Exterior Pipe Detection", MessageBoxButtons.YesNoCancel);
            DialogResult dr = MessageBox.Show(this, "Do you want the tool to check for exterior pipes?\n\n(warning: This process will take time, depending on the number of elements.)\n\nClicking 'No' will apply all as interior",
                "Exterior Pipe Detection", 
                MessageBoxButtons.YesNoCancel,
                MessageBoxIcon.Question,
                MessageBoxDefaultButton.Button2);
            switch (dr)
            {
                case DialogResult.Yes:
                    // Run exterior detection
                    if (PI_ExteriorDetection.Run(SelectedPipesAndFittings, View3D))
                        break;
                    else
                        return;
                case DialogResult.No:
                    break;
                case DialogResult.Cancel:
                    return;
            }

            SelectedDatabase = Databases[(int)CountryReference.NZ];

            // Read from saved json settings and make PI_ProjectSettings
            string exportPath = _projectSettingsExportPathNZ;
            if (SelectedCountryReference == CountryReference.NZ.ToString())
            {
                exportPath = _projectSettingsExportPathNZ;
            }
            else if (SelectedCountryReference == CountryReference.AU.ToString())
            {
                exportPath = _projectSettingsExportPathAU;
                SelectedDatabase = Databases[(int)CountryReference.AU];
            }
            else if (SelectedCountryReference == CountryReference.SG.ToString())
            {
                exportPath = _projectSettingsExportPathSG;
                SelectedDatabase = Databases[(int)CountryReference.SG];
            }
            else if (SelectedCountryReference == CountryReference.Custom.ToString())
            {
                exportPath = _projectSettingsExportPathCustom;
                if (!String.IsNullOrEmpty(CustomDatabase.FirstOrDefault().CountryReference))
                {
                    MessageBox.Show("Can't find custom database.\n\nPlease make sure to save the custom database.");
                    return;
                }
                //SelectedDatabase = CustomDatabase;

                ProjectSettingsToRun = _jsonHandler.ImportFromJson(exportPath);

                if (ProjectSettingsToRun == null)
                {
                    MessageBox.Show("Can't find custom settings file\nInsulation has not been applied.\n\nPlease make sure to save the custom settings.");
                    return;
                }
            }

            MakeRequest(RequestId.AddInsulations);

        }

        private void RunExteriorDetectionAndSetIsInteriorProperty(List<PI_Element> selectedPipesAndFittings)
        {
            foreach (var pI_Element in selectedPipesAndFittings)
            {

            }
        }

        private void dgv_Settings_CellMouseUp(object sender, DataGridViewCellMouseEventArgs e)
        {
            if (rb_Custom.Checked && e.Button == MouseButtons.Right)
            {
                this.contextMenuStrip1.Show(dgv_Settings, e.Location);
                contextMenuStrip1.Show(Cursor.Position);
            }
        }

        private void contextMenuStrip1_Click(object sender, EventArgs e)
        {
            foreach (DataGridViewRow row in dgv_Settings.SelectedRows)
            {
                if (!row.IsNewRow)
                {
                    dgv_Settings.Rows.RemoveAt(row.Index);
                }
            }
        }

        private void btn_Cancel_Click_1(object sender, EventArgs e)
        {
            this.Close();
        }

        private void ActiveButton(Button btn)
        {
            var buttons = new List<Button>();
            buttons.Add(btn_SelectByName);
            buttons.Add(btn_SelectSystemFromRevit);
            buttons.Add(btn_SelectElementsFromRevit);
            foreach (Button b in buttons)
            {
                if (b.Name != btn.Name)
                    b.BackColor = Color.LightGray;
                else
                    b.BackColor = Color.FromArgb(18, 168, 178);
            }
        }

        private void btn_SelectByName_Click(object sender, EventArgs e)
        {
            ActiveButton(btn_SelectByName);

            gb_BySystem.Enabled = true;
            tv_PipeSystemList.Visible = true;

            dgv_SelectedELements.DataSource = null;
            ShowOrHideRefreshButton();
        }

        private void btn_SelectSystemFromRevit_Click(object sender, EventArgs e)
        {
            ActiveButton(btn_SelectSystemFromRevit);

            gb_BySystem.Enabled = false;
            tv_PipeSystemList.Visible = false;

            MessageBox.Show("Please select a pipe or a fitting from a system");

            Hide();
            ModelessPipeInsulationHandler.SelectOneElementInModel(Uiapp, _logger);

            if (SelectedElementInSystem != null)
            {
                SelectedSystemNames = new List<string>();
                var system = _pipeSystems.Find(x => x.Name == SelectedElementInSystem.get_Parameter(BuiltInParameter.RBS_SYSTEM_NAME_PARAM)?.AsString());

                if (system != null)
                {
                    SelectedSystemNames.Add(system.Name);
                    SelectedPipesAndFittings = new List<PI_Element>();

                    try
                    {
                        foreach (Element el in system.PipingNetwork)
                        {
                            SelectedPipesAndFittings.Add(new PI_Element(Doc, el, View3D));
                        }

                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show(ex.Message);
                    }


                    if (SelectedPipesAndFittings != null)
                    {
                        PopulateSelectedElements(SelectedPipesAndFittings);

                        lbl_ElementsCount.Text = $"Number of elements: {SelectedPipesAndFittings.Count}. System Name: {system.Name}.";
                    }
                }
            }
            else
            {
                dgv_SelectedELements.DataSource = null;
            }
            Show();
            BringToFront();

        }

        private void btn_SelectElementsFromRevit_Click(object sender, EventArgs e)
        {
            ActiveButton(btn_SelectElementsFromRevit);

            gb_BySystem.Enabled = false;
            tv_PipeSystemList.Visible = false;

            ModelessPipeInsulationHandler.SelectElementsInModel(Uiapp, _logger);

            if (SelectedPipesAndFittings != null)
            {
                PopulateSelectedElements(SelectedPipesAndFittings);

                lbl_ElementsCount.Text = $"Number of elements: {SelectedPipesAndFittings.Count}";
            }
            else
            {
                dgv_SelectedELements.DataSource = null;
            }

            this.BringToFront();
        }

        private void btn_CreateView_Click(object sender, EventArgs e)
        {
            MakeRequest(RequestId.Create3DView);
        }

        private void btn_RemoveAllInsulation_Click(object sender, EventArgs e)
        {
            if (SelectedPipesAndFittings == null)
            {
                MessageBox.Show("No Pipes or fittings were found.\nPlease make sure that the elements you want to apply are listed in the list.");
            }
            else
            {
                MakeRequest(RequestId.RemoveAllInsulationsAndLinings);
            }
        }

        private void dgv_Settings_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            var senderGrid = (DataGridView)sender;

            if (e.ColumnIndex == SettingsColumn.Index &&
                senderGrid.Rows[e.RowIndex].Cells[SettingsColumn.Index] is DataGridViewButtonCell)
            {
                var isCustom = false;
                var databaseToUse = SelectedDatabase;
                if (rb_Custom.Checked)
                {
                    isCustom = true;
                    databaseToUse = CustomDatabase;
                }

                try
                {
                    using (var databaseForm = new FrmDatabaseSettings(senderGrid.Rows[e.RowIndex].Cells[TypeMark.Index].Value?.ToString() ?? string.Empty, 
                        databaseToUse, _projectCustomDatabaseExportPath, isCustom))
                    {
                        if (!databaseForm.TypeFound)
                        {
                            return; 
                        }

                        if (databaseForm.ShowDialog() == DialogResult.OK)
                        {
                            CustomDatabase = databaseForm.CustomDatabase;
                        }
                    }
                }
                catch (Exception ex)
                {

                }
                
            }
        }

        private void cb_SystemSize_SelectedIndexChanged(object sender, EventArgs e)
        {
            SystemSizeToSet = cb_SystemSize.SelectedItem.ToString();
        }

        private void tb_Search_Leave(object sender, EventArgs e)
        {
            tb_Search.Text = _searchbarPlaceholderText;
        }

        private void tb_Search_TextChanged(object sender, EventArgs e)
        {
            if (tb_Search.Text == "")
            {
                tv_PipeSystemList.CollapseAll();
                foreach (TreeNode node in tv_PipeSystemList.Nodes)
                {
                    foreach (TreeNode subNode in node.Nodes)
                    {
                        subNode.Checked = false;
                        subNode.BackColor = Color.White;
                        subNode.ForeColor = Color.Black;
                    }
                }
            }
            else if (tb_Search.Text == _searchbarPlaceholderText) { }
            else
                FindByText();
        }

        private void FindByText()
        {
            TreeNodeCollection nodes = tv_PipeSystemList.Nodes;
            foreach (TreeNode n in nodes)
            {
                //n.Collapse();
                FindRecursive(n);
            }
        }

        private void FindRecursive(TreeNode treeNode)
        {
            foreach (TreeNode tn in treeNode.Nodes)
            {
                tn.BackColor = Color.White;
                tn.ForeColor = Color.Black;
                // if the text properties match, color the item
                if (tn.Text.IndexOf(tb_Search.Text, StringComparison.OrdinalIgnoreCase) >= 0)
                {
                    //treeNode.Expand();
                    tn.BackColor = Color.FromArgb(141, 14, 132);
                    tn.ForeColor = Color.White;

                }
                FindRecursive(tn);
            }
        }

        private void btn_All_Click(object sender, EventArgs e)
        {
            tv_PipeSystemList.ExpandAll();
            var addedNodes = new List<TreeNode>();
            var removedNodes = new List<TreeNode>();

            foreach (TreeNode node in tv_PipeSystemList.Nodes)
            {
                if (!node.Checked)
                    node.Checked = true;
                addedNodes.Add(node);
            }
            changeSelection(addedNodes, removedNodes);

            ShowOrHideRefreshButton();
        }

        private void btn_None_Click(object sender, EventArgs e)
        {
            var addedNodes = new List<TreeNode>();
            var removedNodes = new List<TreeNode>();

            foreach (TreeNode node in tv_PipeSystemList.Nodes)
            {
                if (node.Checked)
                    node.Checked = false;
                removedNodes.Add(node);
            }
            changeSelection(addedNodes, removedNodes);

            ShowOrHideRefreshButton();

            lbl_ElementsCount.Text = $"Number of elements: 0";
        }
    }


}