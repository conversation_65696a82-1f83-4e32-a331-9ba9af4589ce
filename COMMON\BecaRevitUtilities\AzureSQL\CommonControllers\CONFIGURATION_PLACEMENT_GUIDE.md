# Configuration File Placement Guide

## Overview
This guide explains the best practices for placing encrypted configuration files in the Beca Revit secure configuration system.

## 🎯 **Recommended Approach: Multi-Location Fallback**

The system now uses a **priority-based fallback system** that provides flexibility for different scenarios:

### **Priority Order:**
1. **📦 Next to .dll (Deployment)** - Primary location
2. **🔧 Project directory (Development)** - Development convenience  
3. **👤 User profile (Secure fallback)** - Personal secure storage

## **Location Details**

### **1. Next to .dll (Primary - Recommended for Production)**
```
📁 YourApplication/
├── 📄 YourApplication.exe
├── 📄 YourApplication.dll
├── 🔒 PipeInsulation.config          ← Config file here
├── 🔒 BecaAzureSQL_DevaBot.config    ← Config file here
└── 📄 Other.dll
```

**✅ Advantages:**
- **Environment-specific configurations** (Dev, Test, Prod can have different configs)
- **Secrets not in source control** (security best practice)
- **Deployment flexibility** (each environment gets its own config)
- **Runtime configuration** (can be updated without rebuilding)

**❌ Considerations:**
- Requires manual setup for each deployment
- Can be lost during deployment if not properly managed

### **2. Project Directory (Development Convenience)**
```
📁 RevitCore V3/
├── 📁 Config/
│   └── 📁 Secure/
│       ├── 🔒 PipeInsulation.config          ← Config file here
│       └── 🔒 BecaAzureSQL_DevaBot.config    ← Config file here
├── 📁 MEP.PipeInsulation/
├── 📁 COMMON/
└── 📄 RevitCore.sln
```

**✅ Advantages:**
- **Easy development setup** (shared across team)
- **Version controlled location** (consistent for all developers)
- **Automatic discovery** during development

**❌ Considerations:**
- **Should NOT contain production secrets** (security risk)
- Same config for all developers (not environment-specific)

### **3. User Profile (Secure Personal Storage)**
```
📁 C:\Users\<USER>\.beca\revit\
├── 🔒 PipeInsulation.config          ← Config file here
└── 🔒 BecaAzureSQL_DevaBot.config    ← Config file here
```

**✅ Advantages:**
- **User-specific configurations** (personal development settings)
- **Secure location** (protected by user permissions)
- **No accidental commits** (outside project directory)

**❌ Considerations:**
- Manual setup required for each user
- Not shared across team members

## **🏗️ Setup Recommendations by Environment**

### **Development Environment**
```csharp
// Option 1: Create in project directory for team sharing (non-production secrets only)
string projectConfigDir = @"C:\YourProject\Config\Secure";
Directory.CreateDirectory(projectConfigDir);

// Option 2: Create in user profile for personal settings
string userConfigDir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.UserProfile), ".beca", "revit");
Directory.CreateDirectory(userConfigDir);

// Setup development configuration
var provider = SecureConnectionProviderFactory.CreatePipeInsulationProvider();
provider.CreateConfiguration("dev-server", "dev-database", "dev-user", "dev-password");
```

### **Production Environment**
```csharp
// Always place next to the deployed .dll files
// This happens automatically when you call CreateConfiguration()
var provider = SecureConnectionProviderFactory.CreatePipeInsulationProvider();
provider.CreateConfiguration("prod-server", "prod-database", "prod-user", "prod-password");

// The config file will be created next to the executing assembly
```

### **Testing Environment**
```csharp
// Each test environment gets its own configuration
var provider = SecureConnectionProviderFactory.CreatePipeInsulationProvider();
provider.CreateConfiguration("test-server", "test-database", "test-user", "test-password");
```

## **🔧 Implementation Examples**

### **Check Current Configuration Location**
```csharp
public static void ShowCurrentConfigLocation()
{
    try
    {
        var provider = SecureConnectionProviderFactory.CreatePipeInsulationProvider();
        string connectionString = provider.GetConnectionString();
        
        // The system will log which location was used
        Console.WriteLine("Configuration loaded successfully from one of the fallback locations");
    }
    catch (Exception ex)
    {
        Console.WriteLine($"No configuration found in any location: {ex.Message}");
    }
}
```

### **Setup Development Environment**
```csharp
public static void SetupDevelopmentConfig()
{
    // Create project-level config directory (add to .gitignore!)
    string projectRoot = FindProjectRoot();
    string configDir = Path.Combine(projectRoot, "Config", "Secure");
    Directory.CreateDirectory(configDir);
    
    // Create development configuration
    var provider = SecureConnectionProviderFactory.CreatePipeInsulationProvider();
    bool success = provider.CreateConfiguration(
        server: "dev-server.database.windows.net",
        database: "DevDatabase",
        username: "dev-user",
        password: "dev-password"
    );
    
    if (success)
    {
        Console.WriteLine($"Development configuration created in: {configDir}");
        Console.WriteLine("⚠️  Remember to add Config/Secure/ to .gitignore!");
    }
}
```

### **Setup Production Deployment**
```csharp
public static void SetupProductionConfig(string server, string database, string username, string password)
{
    // This will create the config file next to the executing assembly
    var provider = SecureConnectionProviderFactory.CreatePipeInsulationProvider();
    bool success = provider.CreateConfiguration(server, database, username, password);
    
    if (success)
    {
        string assemblyLocation = Assembly.GetExecutingAssembly().Location;
        string assemblyDir = Path.GetDirectoryName(assemblyLocation);
        Console.WriteLine($"Production configuration created in: {assemblyDir}");
    }
}
```

## **🔒 Security Best Practices**

### **1. .gitignore Configuration**
Add to your `.gitignore` file:
```gitignore
# Secure configuration files
Config/Secure/
*.config
!*.config.template

# User-specific configurations
.beca/
```

### **2. Template Files**
Create template files for team setup:
```
📁 Config/
├── 📁 Secure/           ← Actual configs (gitignored)
└── 📁 Templates/
    ├── 📄 PipeInsulation.config.template
    └── 📄 BecaAzureSQL_DevaBot.config.template
```

### **3. Environment Variables (Alternative)**
For highly secure environments, consider using environment variables:
```csharp
// Fallback to environment variables if no config file found
string connectionString = Environment.GetEnvironmentVariable("BECA_PIPE_INSULATION_CONNECTION");
```

## **🚀 Migration Strategy**

### **Phase 1: Setup Development**
1. Create `Config/Secure/` directory in project root
2. Add to `.gitignore`
3. Setup development configurations
4. Test with development team

### **Phase 2: Production Deployment**
1. Deploy applications to production
2. Create production config files next to .dll files
3. Test production configurations
4. Remove hardcoded fallbacks (optional)

### **Phase 3: Cleanup**
1. Remove temporary hardcoded connection strings
2. Update documentation
3. Train team on new configuration management

## **📋 Checklist for Setup**

### **Development Setup:**
- [ ] Create `Config/Secure/` directory
- [ ] Add `Config/Secure/` to `.gitignore`
- [ ] Create development configuration files
- [ ] Test configuration loading
- [ ] Document setup process for team

### **Production Deployment:**
- [ ] Deploy application to target environment
- [ ] Create production configuration files next to .dll
- [ ] Test database connectivity
- [ ] Verify secure configuration loading
- [ ] Document production configuration process

### **Security Verification:**
- [ ] Confirm no secrets in source control
- [ ] Verify config files are encrypted
- [ ] Test configuration file permissions
- [ ] Validate fallback mechanisms work
- [ ] Review access logs for configuration access

## **🎯 Final Recommendation**

**For Production: Use "Next to .dll" approach** ✅
- Best security (environment-specific, not in source control)
- Deployment flexibility
- Runtime configuration updates

**For Development: Use hybrid approach** ✅
- Project directory for shared development settings (non-production secrets)
- User profile for personal configurations
- Automatic fallback system handles both scenarios

The enhanced implementation provides the best of both worlds with intelligent fallback logic!
