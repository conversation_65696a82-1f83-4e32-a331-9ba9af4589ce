﻿using MEP.PipeInsulation.Database;
using MEP.PipeInsulation.ViewModels;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PipeInsulation.UI.Views.DataSources
{
    public static class PI_TableHelper
    {
        public static DataTable GetViewTable(PI_MainViewModel viewModel, string selectedDataSource, string selectedDesignLevel)
        {
            DataTable viewTable = new DataTable();
            var selectedSource = selectedDataSource + selectedDesignLevel;
            switch (selectedSource)
            {
                case "NZ+AUBase":
                    viewTable = ConvertToViewTable(GetSourceDataTable(viewModel.Data.PI_LookupMaster, "NZ+AU", "Base"));
                    break;
                case "NZ+AUEnhanced":
                    viewTable = ConvertToViewTable(GetSourceDataTable(viewModel.Data.PI_LookupMaster, "NZ+AU", "Enhanced"));
                    break;
                case "SGBase":
                    viewTable = ConvertToViewTable(GetSourceDataTable(viewModel.Data.PI_LookupMaster, "SG", ""));
                    break;
                case "CustomBase":
                    viewTable = ConvertToViewTable(GetSourceDataTable(viewModel.Data.PI_LookupCustom, "Custom", ""));
                    break;
                default:
                    break;
            }

            return viewTable;
        }

        private static DataTable GetSourceDataTable(DataTable table, string country, string designLevel)
        {
            EnumerableRowCollection<DataRow> filteredRows = table.AsEnumerable();
            if (country == PI_SourceNames.NZAU)
            {
                if (designLevel == PI_DesignLevelNames.Base)
                {
                    filteredRows = table.AsEnumerable()
                                    .Where(row => row.Field<string>("Country") == country &&
                                                  (row.Field<string>("DesignLevel") == designLevel || row.Field<string>("DesignLevel") == PI_DesignLevelNames.NotApplicable));
                }
                else
                {
                    filteredRows = table.AsEnumerable()
                                        .Where(row => row.Field<string>("Country") == country &&
                                                      row.Field<string>("DesignLevel") == designLevel);
                }
            }
            else
            {
                filteredRows = table.AsEnumerable()
                                        .Where(row => row.Field<string>("Country") == country);
            }

            // Create a new DataTable with the same structure as the original
            DataTable filteredTable = table.Clone(); // Clone the structure (schema) of the original DataTable

            // Import the filtered rows into the new DataTable
            foreach (var row in filteredRows)
            {
                filteredTable.ImportRow(row);
            }

            return filteredTable;
        }

        private static DataTable ConvertToViewTable(DataTable source)
        {
            DataTable viewTable = new DataTable();

            // Add columns matching the Excel format
            viewTable.Columns.Add("Country");
            viewTable.Columns.Add("System");
            viewTable.Columns.Add("Design Level");
            viewTable.Columns.Add("Commercial");
            viewTable.Columns.Add("Min Dia");
            viewTable.Columns.Add("Max Dia");

            viewTable.Columns.Add("Interior Elastometric");
            viewTable.Columns.Add("Interior Polyethylene");
            viewTable.Columns.Add("Interior Phenolic");
            viewTable.Columns.Add("Interior Fibreglass");

            viewTable.Columns.Add("Exterior Elastometric");
            viewTable.Columns.Add("Exterior Polyethylene");
            viewTable.Columns.Add("Exterior Phenolic");
            viewTable.Columns.Add("Exterior Fibreglass");

            viewTable.Columns.Add("Accoustic");

            // For each row in source → create a "combined row" per unique key
            // GROUP BY: Country, System, Design Level, Commercial, Min Dia, Max Dia
            // First, group the rows:
            var groupedRows = source.AsEnumerable()
                .GroupBy(row => new
                {
                    Country = row.Field<string>("Country"),
                    System = row.Field<string>("System"),
                    DesignLevel = row.Field<string>("DesignLevel"),
                    Commercial = row.Field<string>("Commercial"),
                    MinDia = row["MinDiameter"]?.ToString() ?? "",
                    MaxDia = row["MaxDiameter"]?.ToString() ?? "",
                    Accoustic = row["Acoustic"]?.ToString() ?? ""
                });


            // Now for each group, create ONE combined row
            foreach (var group in groupedRows)
            {
                DataRow viewRow = viewTable.NewRow();

                viewRow["Country"] = group.Key.Country;
                viewRow["System"] = group.Key.System;
                viewRow["Design Level"] = group.Key.DesignLevel;
                viewRow["Commercial"] = group.Key.Commercial;
                viewRow["Min Dia"] = group.Key.MinDia;
                viewRow["Max Dia"] = group.Key.MaxDia;
                viewRow["Accoustic"] = group.Key.Accoustic;

                // Initialize all material columns empty
                viewRow["Interior Elastometric"] = "";
                viewRow["Interior Polyethylene"] = "";
                viewRow["Interior Phenolic"] = "";
                viewRow["Interior Fibreglass"] = "";

                viewRow["Exterior Elastometric"] = "";
                viewRow["Exterior Polyethylene"] = "";
                viewRow["Exterior Phenolic"] = "";
                viewRow["Exterior Fibreglass"] = "";

                // Now process each row in the group → assign to correct column
                foreach (var row in group)
                {
                    string location = row["Location"]?.ToString();
                    string material = row["Material"]?.ToString();
                    string value = row["Value"]?.ToString();

                    string columnName = "";

                    if (location == "Interior")
                    {
                        if (material == "Elastometric") columnName = "Interior Elastometric";
                        else if (material == "Polyethylene") columnName = "Interior Polyethylene";
                        else if (material == "Phenolic") columnName = "Interior Phenolic";
                        else if (material == "Fibreglass") columnName = "Interior Fibreglass";
                    }
                    else if (location == "Exterior")
                    {
                        if (material == "Elastometric") columnName = "Exterior Elastometric";
                        else if (material == "Polyethylene") columnName = "Exterior Polyethylene";
                        else if (material == "Phenolic") columnName = "Exterior Phenolic";
                        else if (material == "Fibreglass") columnName = "Exterior Fibreglass";
                    }

                    if (!string.IsNullOrEmpty(columnName))
                    {
                        viewRow[columnName] = value;
                    }
                }

                // Add the combined row to the viewTable
                viewTable.Rows.Add(viewRow);
            }

            return viewTable;
        }

        public static DataTable ConvertBackToOriginal(DataTable viewTable, bool includeBUID = false, string buid = "")
        {
            DataTable originalTable = new DataTable();

            // Add original columns
            if (includeBUID)
                originalTable.Columns.Add("BUID");

            originalTable.Columns.Add("Country");
            originalTable.Columns.Add("System");
            originalTable.Columns.Add("DesignLevel");
            originalTable.Columns.Add("Commercial");
            originalTable.Columns.Add("Acoustic");
            originalTable.Columns.Add("Material");
            originalTable.Columns.Add("Location");
            originalTable.Columns.Add("MinDiameter");
            originalTable.Columns.Add("MaxDiameter");
            originalTable.Columns.Add("Value");

            // Define material columns and mapping to Material + Location
            var materialColumns = new[]
            {
                new { ColumnName = "Interior Elastometric", Material = "Elastometric", Location = "Interior" },
                new { ColumnName = "Interior Polyethylene", Material = "Polyethylene", Location = "Interior" },
                new { ColumnName = "Interior Phenolic", Material = "Phenolic", Location = "Interior" },
                new { ColumnName = "Interior Fibreglass", Material = "Fibreglass", Location = "Interior" },

                new { ColumnName = "Exterior Elastometric", Material = "Elastometric", Location = "Exterior" },
                new { ColumnName = "Exterior Polyethylene", Material = "Polyethylene", Location = "Exterior" },
                new { ColumnName = "Exterior Phenolic", Material = "Phenolic", Location = "Exterior" },
                new { ColumnName = "Exterior Fibreglass", Material = "Fibreglass", Location = "Exterior" }
            };

            // Process each row in the view table
            foreach (DataRow row in viewTable.Rows)
            {
                foreach (var column in materialColumns)
                {
                    string value = row[column.ColumnName]?.ToString();

                    // Only create row if value is not empty or whitespace
                    if (!string.IsNullOrWhiteSpace(value))
                    {
                        DataRow originalRow = originalTable.NewRow();

                        if (includeBUID)
                            originalRow["BUID"] = buid;

                        originalRow["Country"] = row["Country"];
                        originalRow["System"] = row["System"];
                        originalRow["DesignLevel"] = row["Design Level"];
                        originalRow["Commercial"] = row["Commercial"];
                        originalRow["Acoustic"] = row["Accoustic"];
                        originalRow["Material"] = column.Material;
                        originalRow["Location"] = column.Location;
                        originalRow["MinDiameter"] = row["Min Dia"];
                        originalRow["MaxDiameter"] = row["Max Dia"];
                        originalRow["Value"] = value;

                        originalTable.Rows.Add(originalRow);
                    }
                }
            }

            return originalTable;

        }
    }
}
