﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Plumbing;
using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using Common.UI.Forms;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using MEP.PipeInsulation.CoreLogic;
using MEP.PipeInsulation.CoreLogic.InteriorExteriorDetection;
using MEP.PipeInsulation.CoreLogic.LogicEnhanced;
using MEP.PipeInsulation.Database;
using MEP.PipeInsulation.Models;
using MEP.PipeInsulation.UI.Views.ViewHandlers;
using Microsoft.Office.Interop.Excel;
using Nice3point.Revit.Extensions;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Data;
using DataTable = System.Data.DataTable;
using RequestHandler = MEP.PipeInsulation.UI.Views.ViewHandlers.RequestHandler;
using RequestId = MEP.PipeInsulation.UI.Views.ViewHandlers.RequestId;

namespace MEP.PipeInsulation.ViewModels
{
    public partial class PI_MainViewModel : ObservableObject
    {
        private readonly RequestHandler _handler;
        private readonly ExternalEvent _externalEvent;
        private readonly UIApplication _uiapp;
        private readonly BecaActivityLoggerData _logger;

        // Radio button selections for DataSource filtering
        [ObservableProperty]
        [NotifyPropertyChangedFor(nameof(FilteredPipeSystems))]
        private bool _isNZAUBase = true; // Default selection

        [ObservableProperty]
        [NotifyPropertyChangedFor(nameof(FilteredPipeSystems))]
        private bool _isNZAUEnhanced;

        [ObservableProperty]
        [NotifyPropertyChangedFor(nameof(FilteredPipeSystems))]
        private bool _isSG;

        [ObservableProperty]
        [NotifyPropertyChangedFor(nameof(FilteredPipeSystems))]
        private bool _isCustom;

        // Radio button selections for Commercial/Non-Commercial selections
        [ObservableProperty]
        [NotifyPropertyChangedFor(nameof(FilteredPipeSystems))]
        private bool _isCommercial = true; // Default selection

        [ObservableProperty]
        [NotifyPropertyChangedFor(nameof(FilteredPipeSystems))]
        private bool _isNonCommercial;

        // Selected piping system to update piping elements
        [ObservableProperty]
        [NotifyPropertyChangedFor(nameof(PI_Elements))]
        [NotifyPropertyChangedFor(nameof(AllFilteredElements))]
        private PI_PipingSystem _selectedPipingSystem;

        [ObservableProperty]
        [NotifyPropertyChangedFor(nameof(FilteredPipeSystems))] // Triggers when SearchText changes
        private string _searchText;

        [ObservableProperty]
        [NotifyPropertyChangedFor(nameof(AllFilteredElements))] // Triggers when FilteredPipeSystems changes
        private ObservableCollection<PI_PipingSystem> _filteredPipeSystems;

        [ObservableProperty]
        [NotifyPropertyChangedFor(nameof(FilteredPipeSystems))] // Triggers when FilteredPipeSystems changes
        private ObservableCollection<PI_PipingSystem> _selectedPipeSystems;

        private ObservableCollection<PI_Element> _tempCombinedElements;

        // Derived property for PI_Elements (no backing field needed)
        public ObservableCollection<PI_Element> PI_Elements
        {
            get
            {
                if (_tempCombinedElements != null && SelectedPipingSystem != null)
                {
                    return _tempCombinedElements;
                }

                var elements = SelectedPipingSystem?.PI_Elements ?? new ObservableCollection<PI_Element>();
                return elements;
            }
        }

        [ObservableProperty]
        private string _selectedElementsInfo;
        [ObservableProperty]
        private string _selectedTypesInfo;

        // Computed property (updated reactively)
        [ObservableProperty]
        private ObservableCollection<PI_Element> _allFilteredElements = new();

        [ObservableProperty]
        private ObservableCollection<TreeViewPipeSystem> _treeViewPipeSystems = new();

        [ObservableProperty]
        private CSVLookup _selectedCSVLookup;
        [ObservableProperty]
        private string _selectedDataSource;
        [ObservableProperty]
        private string _selectedDesignLevel;
        [ObservableProperty]
        private string _selectedCommercial;
        [ObservableProperty]
        private DataTable _viewTable;
        [ObservableProperty]
        private DataTable _pI_LookupCombined;

        //[ObservableProperty]
        //private bool _isAllSelected;

        public List<PI_PipingSystem> AllPipeSystems { get; set; }
        public ObservableCollection<PI_Element> ManuallySelectedElements { get; } = new(); // When select random PI elements in Revit (wip)

        public string ProjectName { get; set; }
        public PI_Data Data { get; set; }
        public Element SelectedInsulationType { get; set; }
        public string PI_SelectedDataSource { get; set; }

        bool _showMaterialChangeWarning = true;

        public PI_MainViewModel(ExternalEvent exEvent, RequestHandler handler, BecaActivityLoggerData logger, UIApplication uiapp, PI_Data data)
        {
            _handler = handler;
            _externalEvent = exEvent;
            _uiapp = uiapp;
            _logger = logger;
            Data = data;
            PI_LookupCombined = Data.PI_LookupCombined;

            AllPipeSystems = Data.AllPipingSystems;
            FilteredPipeSystems = new ObservableCollection<PI_PipingSystem>(AllPipeSystems);
            SelectedInsulationType = GetInsulationType(_uiapp.ActiveUIDocument.Document);
            
            SelectedDataSource = GetSelectedDataSource(data);

            UpdateTreeViewPipeSystems();

            ProjectName = _uiapp.ActiveUIDocument.Document.ProjectInformation.get_Parameter(BuiltInParameter.PROJECT_NAME).AsString();
            SelectedDataSource = Data.PI_SelectedDataSource;
            SelectedDesignLevel = PI_DesignLevelNames.Base;
        }

        private string GetSelectedDataSource(PI_Data data)
        {
            if (data.PI_SelectedDataSource== PI_DataSource.NZ_AU_Base.ToString())
            {
                IsNZAUBase = true;
                return PI_SourceNames.NZAU;
            }
            else if (data.PI_SelectedDataSource == PI_DataSource.NZ_AU_Enhanced.ToString())
            {
                IsNZAUEnhanced = true;
                return PI_SourceNames.NZAU;
            }
            else if (data.PI_SelectedDataSource == PI_DataSource.SG.ToString())
            {
                IsSG = true;
                return PI_SourceNames.SG;
            }
            else if (data.PI_SelectedDataSource == PI_DataSource.Custom.ToString())
            {
                IsCustom = true;
                return PI_SourceNames.Custom;
            }
            return PI_SourceNames.NZAU;
        }

        partial void OnIsNZAUBaseChanged(bool value) => FilterPipeSystems();
        partial void OnIsNZAUEnhancedChanged(bool value) => FilterPipeSystems();
        partial void OnIsSGChanged(bool value) => FilterPipeSystems();
        partial void OnIsCommercialChanged(bool value) => FilterPipeSystems();
        partial void OnIsCustomChanged(bool value) => FilterPipeSystems();
        partial void OnIsNonCommercialChanged(bool value) => FilterPipeSystems();
        partial void OnSearchTextChanged(string value) => FilterPipeSystems();
        partial void OnSelectedPipingSystemChanged(PI_PipingSystem value) => UpdatePIElements();
        partial void OnPI_LookupCombinedChanged(DataTable value) => UpdatePIElements();

        // Called when FilteredPipeSystems changes (via PropertyChanged event)
        partial void OnFilteredPipeSystemsChanged(ObservableCollection<PI_PipingSystem>? oldValue, ObservableCollection<PI_PipingSystem>? newValue)
        {
            SubscribeToPipeSystemSelection(FilteredPipeSystems);
            UpdateCollections();
            UpdateTreeViewPipeSystems();
        }

        private void FilterPipeSystems()
        {
            // Update SelectedDataSource, SelectedDesignLevel, SelectedCommercial
            if (IsNZAUBase)
            {
                SelectedDataSource = PI_SourceNames.NZAU;
                PI_SelectedDataSource = PI_DataSource.NZ_AU_Base.ToString();
                MakeRequest(RequestId.UpdatePI_SelectedDataSource);

                SelectedDesignLevel = PI_DesignLevelNames.Base;
            }
            else if (IsNZAUEnhanced)
            {
                SelectedDataSource = PI_SourceNames.NZAU;
                PI_SelectedDataSource = PI_DataSource.NZ_AU_Enhanced.ToString();
                MakeRequest(RequestId.UpdatePI_SelectedDataSource);

                SelectedDesignLevel = PI_DesignLevelNames.Enhanced;
            }
            else if (IsSG)
            {
                SelectedDataSource = PI_SourceNames.SG;
                PI_SelectedDataSource = PI_DataSource.SG.ToString();
                MakeRequest(RequestId.UpdatePI_SelectedDataSource);

                SelectedDesignLevel = PI_DesignLevelNames.Base;
            }
            else if (IsCustom)
            {
                SelectedDataSource = PI_SourceNames.Custom;
                PI_SelectedDataSource = PI_DataSource.Custom.ToString();
                MakeRequest(RequestId.UpdatePI_SelectedDataSource);

                SelectedDesignLevel = PI_DesignLevelNames.Base;
            }

            if (IsNonCommercial)
            {
                SelectedCommercial = PI_CommercialNames.NonCommercial;
            }
            else
            {
                SelectedCommercial = PI_CommercialNames.Commercial;
            }

            // Update SelectedPipeSystems based on SelectedDataSource, SelectedDesignLevel, SelectedCommercial
            foreach (var ps in AllPipeSystems)
            {
                ps.SelectedDataSource = SelectedDataSource;
                ps.SelectedCommercial = SelectedCommercial;
                ps.SelectedDesignLevel = SelectedDesignLevel;
                ps.UpdateSystemStypeNameOptions();
            }

            // Update FilteredPipeSystems
            IEnumerable<PI_PipingSystem> filtered = new List<PI_PipingSystem>();
            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                filtered = AllPipeSystems.Where(ps =>
                    ps.Name.IndexOf(SearchText, StringComparison.OrdinalIgnoreCase) >= 0);
            }

            if (filtered.Count() > 0)
            {
                var newFilteredSystems = new ObservableCollection<PI_PipingSystem>();
                foreach (var system in filtered)
                {
                    system.IsSelected = false;
                    newFilteredSystems.Add(system);
                }

                FilteredPipeSystems = newFilteredSystems;
            }
            else
            {
                FilteredPipeSystems = new ObservableCollection<PI_PipingSystem>(AllPipeSystems);
            }

        }

        private void SubscribeToPipeSystemSelection(IEnumerable<PI_PipingSystem> systems)
        {
            if (systems == null) return;
            foreach (var system in systems)
            {
                system.PropertyChanged -= PipeSystem_PropertyChanged;
                system.PropertyChanged += PipeSystem_PropertyChanged;
            }
        }

        private void PipeSystem_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            switch (e.PropertyName)
            {
                case nameof(PI_PipingSystem.IsSelected):
                    UpdateCollections();
                    UpdateThicknessForSelectedSystems();
                    break;

                case nameof(PI_PipingSystem.SelectedMaterial):
                    WarningMessageForMaterialChangePerSession();
                    UpdatePIElements();
                    break;
                case nameof(PI_PipingSystem.SelectedSystemTypeName):
                    UpdatePIElements();
                    break;
            }
        }

        private void WarningMessageForMaterialChangePerSession()
        {
            // Warning to be displayed the first time (in a session) that the user changes insulation material to anything other than “worst case”
            if (_showMaterialChangeWarning)
            {
                MessageBox.Show("WARNING:\nBeca master C1715/C1960 specs allow contractor to choose insulation material from list of options." +
                "\n\nModelling specific material could create discrepancy between model/drawings and spec." +
                "\n\nOnly select specific material if project spec has been edited to restrict the contractor’s choice to the modelled material.",
                "Material change", MessageBoxButtons.OK, MessageBoxIcon.Warning);

                _showMaterialChangeWarning = false;
            }
            
        }

        /// <summary>
        /// Updates calculated thickness for all elements in the selected systems.
        /// </summary>
        private void UpdateThicknessForSelectedSystems()
        {
            // Ensure SelectedPipeSystems is available and iterate
            foreach (var selectedPipingSystem in SelectedPipeSystems)
            {
                UpdateCalculatedThicknessOnPI_Elements(selectedPipingSystem);
            }
        }

        /// <summary>
        /// Updates calculated thickness for a specific piping system's elements.
        /// </summary>
        private void UpdateCalculatedThicknessOnPI_Elements(PI_PipingSystem selectedPipingSystem)
        {
            // Get systems with the same type as the selected piping system
            var systemsOfSameType = GetSystemsOfSameType(selectedPipingSystem.SystemTypeName);

            foreach (var system in systemsOfSameType)
            {
                foreach (var element in system.PI_Elements)
                {
                    // Calculate thickness using helper method
                    element.CalculatedThickness = CalculateElementThickness(element, selectedPipingSystem);
                }
            }
        }

        /// <summary>
        /// Updates the PI elements display and related collections.
        /// Called when material or other criteria change
        /// </summary>
        private void UpdatePIElements()
        {
            if (SelectedPipingSystem == null) return;

            // Get systems matching the selected piping system's type
            var systemsOfSameType = GetSystemsOfSameType(SelectedPipingSystem.SystemTypeName);

            // Update calculated thickness for each element
            foreach (var system in systemsOfSameType)
            {
                foreach (var element in system.PI_Elements)
                {
                    element.CalculatedThickness = CalculateElementThickness(element, SelectedPipingSystem);
                }
            }

            // Combine elements into a temporary collection for display
            _tempCombinedElements = systemsOfSameType
                .SelectMany(ps => ps.PI_Elements)
                .ToObservableCollection();

            // Notify UI about changes to PI_Elements
            OnPropertyChanged(nameof(PI_Elements));

            // Refresh the AllFilteredElements collection
            UpdateAllFilteredElements();

        }

        /// <summary>
        /// Returns a list of pipe systems that share the same system type name.
        /// </summary>
        private List<PI_PipingSystem> GetSystemsOfSameType(string systemTypeName)
        {
            return FilteredPipeSystems?
                .Where(ps => ps.SystemTypeName == systemTypeName)
                .ToList() ?? new List<PI_PipingSystem>();
        }

        /// <summary>
        /// Calculates the thickness of a pipe element based on given parameters.
        /// </summary>
        private double CalculateElementThickness(PI_Element element, PI_PipingSystem pipingSystem)
        {
            // Adjust parameters based on whether MatchSource is true or false
            var systemTypeName = pipingSystem.MatchSource
                ? pipingSystem.SystemTypeName
                : pipingSystem.SelectedSystemTypeName;

            return PI_DatabaseFiltering.FilterCalculatedThickness(
                PI_LookupCombined,
                element,
                systemTypeName,
                SelectedDataSource,
                SelectedDesignLevel,
                SelectedCommercial,
                pipingSystem.SelectedMaterial);
        }

        private void UpdateAllFilteredElements()
        {
            var elements = FilteredPipeSystems?
                .Where(ps => ps.IsSelected)
                .SelectMany(ps => ps.PI_Elements)
                .ToList() ?? new();

            AllFilteredElements.Clear();

            foreach (var element in elements)
            {
                AllFilteredElements.Add(element);
            }
        }

        private void UpdateSelectedPipeSystems()
        {
            // Get all selected pipe systems
            var allSelected = FilteredPipeSystems?.Where(ps => ps.IsSelected).ToList() ?? new List<PI_PipingSystem>();

            // Group by SystemTypeName and take the first item from each group
            var uniqueBySystemType = allSelected
                .GroupBy(ps => ps.SystemTypeName)
                .Select(group => group.First())
                .ToObservableCollection();

            SelectedPipeSystems = uniqueBySystemType;
        }

        private void UpdateSelectedElementsInfo()
        {
            var selectedSystems = FilteredPipeSystems?
                    .Where(ps => ps.IsSelected)
                    .Select(ps => ps.Name);
            SelectedElementsInfo = $"Selected systems count: {selectedSystems.Count()}\nSelected systems: {string.Join(", ", selectedSystems ?? Enumerable.Empty<string>())}.\nTotal elements: {AllFilteredElements?.Count ?? 0}.";
            SelectedTypesInfo = $"Selected types count: {SelectedPipeSystems.Count}";
        }

        private void UpdateTreeViewPipeSystems()
        {
            TreeViewPipeSystems.Clear();

            // Create a flat list for the TreeView
            foreach (var system in FilteredPipeSystems)
            {
                TreeViewPipeSystems.Add(new TreeViewPipeSystem
                {
                    Name = system.Name,
                    IsSelected = system.IsSelected,
                    OriginalSystem = system
                });
            }
        }

        private void UpdateCollections()
        {
            UpdateSelectedPipeSystems();
            UpdateAllFilteredElements();
            UpdateSelectedElementsInfo();
        }

        private Element GetInsulationType(Document doc)
        {
            return new FilteredElementCollector(doc).OfClass(typeof(PipeInsulationType)).ToElements().FirstOrDefault();
        }

        [RelayCommand]
        private void SelectElementsFromRevit() => MakeRequest(RequestId.SelectElementsFromRevit);

        [RelayCommand]
        private void SelectSystemFromRevit() => MakeRequest(RequestId.SelectSystemFromRevit);

        [RelayCommand]
        private void Create3DView() => MakeRequest(RequestId.Create3DView);

        [RelayCommand]
        private void AddInsulations() => MakeRequest(RequestId.AddInsulations);

        [RelayCommand]
        private void RemoveAllInsulations() => MakeRequest(RequestId.RemoveAllInsulations);

        [RelayCommand]
        private void DetectInteriorExterior()
        {
            var doc = _uiapp.ActiveUIDocument.Document;
            var linkManager = new LinkManager(doc);
            var detection = new InteriorExteriorDetection(Data.View3D);

            // Load links
            var loadedLinks = linkManager.GetLoadedLinks();

            // Check if no linked files are found
            if (!linkManager.GetLoadedLinks().Any() && !MessageBoxHelper.PromptNoLinkedFiles())
                return;

            // Check element count and warn if necessary
            int nCount = AllFilteredElements.Count();
            if (nCount > 250 && !MessageBoxHelper.PromptLargeElementCount(nCount))
                return;

            detection.Run(AllFilteredElements);

            // Recalc CalculatedThickness
            UpdatePIElements();
        }

        [RelayCommand]
        private void ResetAllInterior()
        {
            PipeInsulationHelper.RestAllInterior(AllFilteredElements);

            // Recalc CalculatedThickness
            UpdatePIElements();
        }

        [RelayCommand]
        private void SelectAllFilteredSystems()
        {
            SelectAllSystems(true);
            OnPropertyChanged(nameof(TreeViewPipeSystems));
        }

        [RelayCommand]
        private void ClearSelection()
        {
            SelectAllSystems(false);
            OnPropertyChanged(nameof(TreeViewPipeSystems));
        }

        private void SelectAllSystems(bool selectAll)
        {
            // Update the TreeViewPipeSystems to reflect the changes
            foreach (var treeViewSystem in TreeViewPipeSystems)
            {
                treeViewSystem.IsSelected = selectAll;
            }
        }

        private void MakeRequest(RequestId request)
        {
            _handler.Request.Make(request);
            _externalEvent.Raise();
        }
    }

    public static class ObservableCollectionExtensions
    {
        public static ObservableCollection<T> ToObservableCollection<T>(this IEnumerable<T> source)
        {
            return new ObservableCollection<T>(source);
        }
    }

}
