﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Plumbing;
using Autodesk.Revit.UI;
using Autodesk.Revit.UI.Selection;
using BecaActivityLogger.CoreLogic.Data;
using BecaRevitUtilities;
using MEP.PipeInsulation.CoreLogic;
using MEP.PipeInsulation.CoreLogic.InteriorExteriorDetection;
using MEP.PipeInsulation.Models;
using MEP.PipeInsulation.ViewModels;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using PipingSystem = Autodesk.Revit.DB.Plumbing.PipingSystem;
using TaskDialog = Autodesk.Revit.UI.TaskDialog;

namespace MEP.PipeInsulation.UI.Views.ViewHandlers
{
    internal class PI_MainWindowHandler
    {
        static UIApplication _uiapp;
        public static UIApplication UIApp { get => _uiapp; }

        static View3D _view3D;
        public static View3D View3D { get => _view3D; }

        static PI_MainViewModel _pI_MainViewModel;
        public static PI_MainViewModel PI_MainViewModel { get => _pI_MainViewModel; }

        static PI_MainWindow _modelessMainWindow;
        public static PI_MainWindow ModelessMainWindow { get => _modelessMainWindow; }

        public static void ShowForm(UIApplication uiapp, BecaActivityLoggerData logger, PI_Data data)
        {
            try
            {
                _uiapp = uiapp;

                if (_modelessMainWindow == null)
                {
                    // A new handler to handle request posting by the dialog
                    RequestHandler handler = new RequestHandler(logger);

                    // External Event for the dialog to use (to post requests)
                    ExternalEvent exEvent = ExternalEvent.Create(handler);

                    _pI_MainViewModel = new PI_MainViewModel(exEvent, handler, logger, uiapp, data);

                    _uiapp = uiapp;
                    _view3D = data.View3D;

                    // We give the objects to the new dialog;
                    // The dialog becomes the owner responsible fore disposing them, eventually.
                    _modelessMainWindow = new PI_MainWindow(_pI_MainViewModel);

                    // Clear the reference on close
                    _modelessMainWindow.Closed += (s, e) => _modelessMainWindow = null;
                    _modelessMainWindow.Show();
                }
                else
                {
                    _modelessMainWindow.Activate();
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show(ex.ToString(), "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        public static void AddInsulations(UIApplication uiapp, BecaActivityLoggerData logger)
        {
            // Check for locked elements in worksharing environment
            var lockedElements = 0;
            var lockedOwners = new List<string>();
            var sb = new StringBuilder();

            try
            {
                var doc = uiapp.ActiveUIDocument.Document;

                foreach (var pi_Element in _pI_MainViewModel.AllFilteredElements)
                {
                    if (pi_Element.IsLocked)
                    {
                        lockedElements++;
                        if (!lockedOwners.Contains(pi_Element.Owner))
                            lockedOwners.Add(pi_Element.Owner);
                    }
                }

                // Run pipe insulation logic
                if (lockedElements > 0)
                {
                    lockedOwners.ForEach(o => sb.AppendLine(o));
                    DialogResult dr = System.Windows.Forms.MessageBox.Show($"Warning:\nThe current selection includes {lockedElements}\nelements locked out by:\n\n{sb}\n\nDo you want to continue and ignore the\nlocked elements?",
                          "Locked elements found", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    switch (dr)
                    {
                        case DialogResult.Yes:
                            RunInsulationPlacementLogic(doc, null);
                            break;
                        case DialogResult.No:
                            break;
                    }
                }
                else
                {
                    RunInsulationPlacementLogic(doc, null);
                }

                logger?.Log("Process end.", LogType.Information);
            }
            catch (Exception e)
            {
                logger?.Log($"Error from removing all insulations: {e.Message}", LogType.Error);
                //telemetry.LogException(e, "AddInsulations", null, null);
            }
        }

        private static void RunInsulationPlacementLogic(Document doc, BecaActivityLoggerData logger)
        {
            var elementsToInsulate = _pI_MainViewModel.AllFilteredElements;

            // Find all PipeInsulation elements in the document
            var insulationCollector = new FilteredElementCollector(doc).OfClass(typeof(Autodesk.Revit.DB.Plumbing.PipeInsulation)).Cast<Autodesk.Revit.DB.Plumbing.PipeInsulation>().ToList();

            // Create Pipe and Insulation Dictionary
            var pipeInsulationDict = new Dictionary<ElementId, Autodesk.Revit.DB.Plumbing.PipeInsulation>();
            foreach (var insulation in insulationCollector)
            {
                Element hostPipe = doc.GetElement(insulation.HostElementId); // Get the associated pipe for the insulation

#if TargetYear2024 || TargetYear2025 || TargetYear2026
                if (hostPipe.Category.BuiltInCategory == BuiltInCategory.OST_PipeCurves || hostPipe.Category.BuiltInCategory == BuiltInCategory.OST_PipeFitting)
#else
                if ((BuiltInCategory)hostPipe.Category.Id.IntegerValue == BuiltInCategory.OST_PipeCurves || (BuiltInCategory)hostPipe.Category.Id.IntegerValue == BuiltInCategory.OST_PipeFitting)
#endif
                {
                    pipeInsulationDict[hostPipe.Id] = insulation; // Add to dictionary
                }
            }

            try
            {
                using (var trans = new Transaction(doc, "Add Insulation"))
                {
                    trans.Start();
                    var appliedCount = 0;
                    int nCount = elementsToInsulate.Count;
                    string progressMessage = "{0} of " + nCount.ToString() + " elements processed...";
                    string caption = "Applying Insulations";
                    using (var pf = new Common.UI.Forms.BecaProgressForm(caption, progressMessage, nCount))
                    {
                        foreach (var pI_Element in elementsToInsulate)
                        {
                            try
                            {
                                if (pI_Element.IsLocked || pI_Element.CalculatedThickness == 0)
                                    continue;

                                if (pI_Element.HasInsulation)
                                {
                                    // Get insulation from dictionary using pI_Element.Element
                                    Autodesk.Revit.DB.Plumbing.PipeInsulation insulation;
                                    if (pipeInsulationDict.TryGetValue(pI_Element.Element.Id, out insulation)) // Try to get insulation
                                    {
                                        Parameter thicknessParam = insulation.get_Parameter(BuiltInParameter.RBS_INSULATION_THICKNESS_FOR_PIPE);
                                        if (thicknessParam != null && !thicknessParam.IsReadOnly)
                                        {
                                            thicknessParam.Set(RevitUnitConvertor.MmToInternal(pI_Element.CalculatedThickness)); // Update thickness
                                            pI_Element.RevitThickness = pI_Element.CalculatedThickness;
                                            appliedCount++;
                                            pf.Increment();
                                        }
                                    }
                                }
                                else
                                {
                                    Autodesk.Revit.DB.Plumbing.PipeInsulation.Create(doc,
                                    pI_Element.Element.Id, _pI_MainViewModel.SelectedInsulationType.Id, RevitUnitConvertor.MmToInternal(pI_Element.CalculatedThickness));
                                    pI_Element.HasInsulation = true;
                                    pI_Element.RevitThickness = pI_Element.CalculatedThickness;
                                    appliedCount++;
                                    pf.Increment();
                                }

                            }
                            catch (Exception)
                            {

                            }

                        }
                    }
                    trans.Commit();

                    var successMessage = $"Applied insulations to\n{appliedCount} elements from {elementsToInsulate.Count} elements";
                    logger?.Log(successMessage, LogType.Information);
                    //telemetry.LogProcessing(new Dictionary<string, string>() { { "Applied Insulations", $"{appliedCount} elements applied." } });
                    TaskDialog.Show("Result", successMessage);
                }
            }
            catch (Exception e)
            {
                TaskDialog.Show("Error", e.Message);
                logger?.Log($"Error from RunInsulationPlacementLogic: {e.Message}", LogType.Error);
                //telemetry.LogException(e, "RunInsulationPlacementLogic", null, null);
            }
        }

        public static void SelectElementsInModel(UIApplication uiapp, BecaActivityLoggerData logger)
        {
            try
            {
                var ids = uiapp.ActiveUIDocument.Selection.PickObjects(ObjectType.Element, "Please select elements").Select(r => r.ElementId);

                UpdateAllFilteredElementsFromSelectedIds(ids);

                logger.Log($"{ids.Count()} elements selected in model.", LogType.Information);
                //telemetry.LogProcessing(new Dictionary<string, string>() { { "Elements selected", $"{selectedPI_Elements.Count} elements." } });

                BringMainWindowToFront();

            }
            catch (Exception e)
            {
                logger.Log($"Error from selection in model: {e.Message}", LogType.Error);
                //telemetry.LogException(e, "SelectElementsInModel", null, null);
            }

        }

        public static void SelectOneElementInModel(UIApplication uiapp, BecaActivityLoggerData logger)
        {
            try
            {
                _pI_MainViewModel.SearchText = "";
                var reference = uiapp.ActiveUIDocument.Selection.PickObject(ObjectType.Element, "Please select a pipe or a fitting from a system");
                if (reference != null)
                {
                    var el = uiapp.ActiveUIDocument.Document.GetElement(reference.ElementId);
#if TargetYear2024 || TargetYear2025 || TargetYear2026
                    if ((el.Category.BuiltInCategory == BuiltInCategory.OST_PipeFitting || el.Category.BuiltInCategory == BuiltInCategory.OST_PipeCurves) && el != null)
#else
                    if (((BuiltInCategory)el.Category.Id.IntegerValue == BuiltInCategory.OST_PipeFitting || (BuiltInCategory)el.Category.Id.IntegerValue == BuiltInCategory.OST_PipeCurves) && el != null)
#endif
                    {
                        var system = _pI_MainViewModel.AllPipeSystems.ToList().Find(x => x.Name == el.get_Parameter(BuiltInParameter.RBS_SYSTEM_NAME_PARAM)?.AsString());
                        if (system == null)
                        {
                            System.Windows.MessageBox.Show("Selected system is not loaded in the current runtime.\nPlease try restarting Pipe Insulation", "System Not Found", MessageBoxButton.OK, MessageBoxImage.Error);
                            return;
                        }
                        
                        system.IsSelected = true;

                        _pI_MainViewModel.SelectedElementsInfo = $"Selected systems count: 1\nSystem name: {system.Name}.\nNumber of elements: {system.PI_Elements.Count}. ";
                        _pI_MainViewModel.SelectedTypesInfo = $"Selected types count: {_pI_MainViewModel.SelectedPipeSystems.Count}";
                    }
                    else
                    {
                        System.Windows.Forms.MessageBox.Show("Please select a pipe or fitting");
                    }
                }

                BringMainWindowToFront();
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show(ex.Message);
                logger.Log($"Error from selection single element in model: {ex.Message}", LogType.Error);
                //telemetry.LogException(ex, "SelectOneElementInModel", null, null);
            }

        }

        public static void Create3DViewHandler(UIApplication uiapp, BecaActivityLoggerData logger)
        {
            View3D systemView = null;
            var viewName = $"PipeInsulationView_{uiapp.Application.Username}";
            try
            {
                using (var t = new Transaction(uiapp.ActiveUIDocument.Document, "Create 3D Pipe Insulation View"))
                {
                    t.Start();
                    systemView = PipeInsulationHelper.Create3DSystemViewWPF(uiapp, PI_MainViewModel, viewName);
                    t.Commit();
                }

                logger.Log($"3D View: {viewName} created.", LogType.Information);
            }
            catch (Exception e)
            {
                logger.Log($"Error from Creating 3D Pipe Insulation View: {e.Message}", LogType.Error);
                //telemetry.LogException(e, "PipeInsulationHelper.Create3DSystemView", null, null);
            }

            uiapp.ActiveUIDocument.ActiveView = systemView;

            try
            {
                using (var t = new Transaction(uiapp.ActiveUIDocument.Document, "Set view detail level"))
                {
                    t.Start();
                    var selectedDuctsAndFittingsId = PipeInsulationHelper.GetSelectedElementIds(PI_MainViewModel);
                    uiapp.ActiveUIDocument.ShowElements(selectedDuctsAndFittingsId);
                    systemView.DetailLevel = ViewDetailLevel.Fine;

                    // Isolate elements
                    systemView.IsolateElementsTemporary(selectedDuctsAndFittingsId);

                    t.Commit();
                }

                logger.Log("View detail level set.", LogType.Information);
            }
            catch (Exception e)
            {
                logger.Log($"Error from Create3DViewHandler: {e.Message}", LogType.Error);
                //telemetry.LogException(e, "Create3DViewHandler", null, null);
            }

        }

        public static void DetectInteriorExterior(UIApplication uiapp, BecaActivityLoggerData logger)
        {

        }

        public static void RemoveAllInsulations(UIApplication uiapp, BecaActivityLoggerData logger)
        {
            try
            {
                if (_pI_MainViewModel.AllFilteredElements == null)
                    return;

                new InsulationPlacementLogic(uiapp.ActiveUIDocument.Document).RemoveAllInsulationsWPF(_pI_MainViewModel);

                logger.Log("All insulation successfully removed.", LogType.Information);
            }
            catch (Exception e)
            {
                logger.Log($"Error from removing all insulations: {e.Message}", LogType.Error);
                //telemetry.LogException(e, "RemoveAllInsulations", null, null);
            }
        }

        public static void UpdateSelectedDataSource(UIApplication uiapp, BecaActivityLoggerData logger)
        {
            var doc = uiapp.ActiveUIDocument.Document;
            using (Transaction trans = new Transaction(doc, "Update Custom String"))
            {
                trans.Start();
                PI_SelectedDataSourceStorage.SaveSelectedDataSourceString(doc, _pI_MainViewModel.PI_SelectedDataSource);

                trans.Commit();
            }
        }

        private static void BringMainWindowToFront()
        {
            if (_modelessMainWindow != null)
            {
                // If it's only true it will stay true
                _modelessMainWindow.Topmost = true;
                _modelessMainWindow.Topmost = false;
                _modelessMainWindow.Activate();
            }
        }

        private static void UpdateAllFilteredElementsFromSelectedIds(IEnumerable<ElementId> ids)
        {
            _pI_MainViewModel.AllFilteredElements.Clear();

            var idSet = new HashSet<ElementId>(ids);

            foreach (var pI_PipingSystem in _pI_MainViewModel.AllPipeSystems)
            {
                foreach (var id in idSet)
                {
                    var matchingElements = pI_PipingSystem.PI_Elements
                        .Where(pI_Element => pI_Element.Element.Id == id);

                    foreach (var pI_Element in matchingElements)
                    {
                        _pI_MainViewModel.AllFilteredElements.Add(pI_Element);
                    }

                    if (matchingElements.Any())
                    {
                        pI_PipingSystem.IsSelected = true;
                    }
                }
            }

            var distinctSystemNames = _pI_MainViewModel.AllFilteredElements
                .Select(fe => fe.SystemName)
                .Distinct();

            _pI_MainViewModel.SelectedElementsInfo =
                $"Selected systems count: {distinctSystemNames.Count()}\nTotal selected systems: {string.Join(", ", distinctSystemNames)}." +
                $"\nTotal elements: {PI_MainViewModel.AllFilteredElements.Count}.";
            _pI_MainViewModel.SelectedTypesInfo = $"Selected types count: {_pI_MainViewModel.SelectedPipeSystems.Count}";
        }
    }
    
}
