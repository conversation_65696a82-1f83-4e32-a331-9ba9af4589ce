﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI.Selection;
using BecaRevitUtilities;
using Common.UI.Forms;
using MEP.PipeInsulation.Models;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PipeInsulation.CoreLogic.InteriorExteriorDetection
{
    public class InteriorExteriorDetection
    {
        private readonly View3D _view3D;

        public InteriorExteriorDetection(View3D view3D)
        {
            _view3D = view3D;
        }

        public void Run(ObservableCollection<PI_Element> allFilteredElements)
        {
            // Get the total count of pI_Element.IsInterior that are false, if the count is the same as allFilteredElements show a message
            var elementCount = allFilteredElements.Count;
            string progressMessage = "{0} of " + elementCount.ToString() + " pipe elements processed...";
            string caption = $"Checking whether elements are interior or exterior.";
            using (BecaProgressForm pf = new BecaProgressForm(caption, progressMessage, elementCount))
            {
                foreach (var pI_Element in allFilteredElements)
                {
                    CalculateInteriorStatus(pI_Element);
                    pf.Increment();
                }
            }

            // Notify the user if all elements are exterior
            CheckIfAllInteriorStatusesAreFalse(allFilteredElements);
        }

        private void CalculateInteriorStatus(PI_Element pI_Element)
        {
#if TargetYear2024 || TargetYear2025 || TargetYear2026
            if (pI_Element.Element.Category.BuiltInCategory == BuiltInCategory.OST_PipeCurves)
#else
    if (pI_Element.Element.Category.Id.IntegerValue == (int)BuiltInCategory.OST_PipeCurves)
#endif
            {
                pI_Element.IsInterior = CheckInteriorForPipeCurve(pI_Element.Element);
            }
            else
            {
                var locationPoint = pI_Element.Element.Location as LocationPoint;
                pI_Element.IsInterior = locationPoint != null && CheckInterior(locationPoint.Point);
            }
        }

        /// <summary>
        /// Checks whether the given pipe based on its curve is interior or exterior.
        /// </summary>
        private bool CheckInteriorForPipeCurve(Element element)
        {
            var locationCurve = element.Location as LocationCurve;
            if (locationCurve == null) return false;

            var startPoint = locationCurve.Curve.GetEndPoint(0);
            var endPoint = locationCurve.Curve.GetEndPoint(1);

            return CheckInterior(startPoint) && CheckInterior(endPoint);
        }

        /// <summary>
        /// Determines if an XYZ point is interior by analyzing boundaries.
        /// </summary>
        private bool CheckInterior(XYZ location)
        {
            double range = UnitConversionUtility.mm_Feet(100); // Convert 100mm to feet

            // Six directions from linke element location
            var baseDirectionX = new XYZ(location.X + range, location.Y, location.Z) - location;
            var baseDirectionY = new XYZ(location.X, location.Y + range, location.Z) - location;
            var baseDirectionZ = new XYZ(location.X, location.Y, location.Z + range) - location;
            var baseDirectionMinX = new XYZ(location.X - range, location.Y, location.Z) - location;
            var baseDirectionMinY = new XYZ(location.X, location.Y - range, location.Z) - location;
            var baseDirectionMinZ = new XYZ(location.X, location.Y, location.Z - range) - location;

            var boundaries = new List<BuiltInCategory>
            {
                BuiltInCategory.OST_Walls,
                BuiltInCategory.OST_Floors,
                BuiltInCategory.OST_Ceilings,
                BuiltInCategory.OST_Roofs
            };

            bool isInterior = true;

            bool foundWalls = true;
            bool foundCeilingOrRoof = true;
            bool foundFloor = true;

            foreach (var bic in boundaries)
            {
                ReferenceIntersector refIntersector = new ReferenceIntersector(new ElementCategoryFilter(bic), FindReferenceTarget.Face, _view3D);
                refIntersector.FindReferencesInRevitLinks = true;

                switch (bic)
                {
                    case BuiltInCategory.OST_Walls:
                        foundWalls = IsBoundaryFound(refIntersector, location, baseDirectionX) &&
                                        IsBoundaryFound(refIntersector, location, baseDirectionMinX) &&
                                        IsBoundaryFound(refIntersector, location, baseDirectionY) &&
                                        IsBoundaryFound(refIntersector, location, baseDirectionMinY);
                        break;
                    case BuiltInCategory.OST_Floors:
                        foundFloor = IsBoundaryFound(refIntersector, location, baseDirectionMinZ);
                        break;
                    case BuiltInCategory.OST_Ceilings:
                        foundCeilingOrRoof = IsBoundaryFound(refIntersector, location, baseDirectionZ);
                        break;
                    case BuiltInCategory.OST_Roofs:
                        foundCeilingOrRoof = IsBoundaryFound(refIntersector, location, baseDirectionZ);
                        break;
                }
            }

            if (!foundWalls)
            {
                isInterior = false;
            }
            else if (!foundCeilingOrRoof || !foundFloor)
            {
                isInterior = false;
            }

            return isInterior;
        }

        private static bool IsBoundaryFound(ReferenceIntersector refIntersector, XYZ location, XYZ direction)
        {
            return refIntersector.FindNearest(location, direction) != null;
        }

        private void CheckIfAllInteriorStatusesAreFalse(ObservableCollection<PI_Element> allFilteredElements)
        {
            bool allAreFalse = allFilteredElements.All(pI_Element => !pI_Element.IsInterior);

            if (allAreFalse)
            {
                MessageBoxHelper.ShowNoInteriorElementsMessage();
            }
        }
    }
}
