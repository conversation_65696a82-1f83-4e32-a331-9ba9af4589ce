using System;
using Microsoft.Data.SqlClient;
using System.Data;

namespace BecaRevitUtilities.AzureSQL.CommonControllers
{
    /// <summary>
    /// Example showing how to integrate the common secure configuration system
    /// This demonstrates the recommended approach for new projects and migration scenarios
    /// </summary>
    public class ExampleDatabaseClient : IDisposable
    {
        private SqlConnection connection;
        private readonly SecureConnectionProvider _connectionProvider;

        /// <summary>
        /// Constructor for a specific project
        /// </summary>
        /// <param name="projectName">Name of the project (used for config file naming)</param>
        /// <param name="connectionStringName">Connection string name in app.config for fallback</param>
        public ExampleDatabaseClient(string projectName, string connectionStringName = null)
        {
            connection = null;
            _connectionProvider = SecureConnectionProviderFactory.CreateProjectProvider(projectName, connectionStringName);
        }

        /// <summary>
        /// Constructor for Pipe Insulation project (maintains compatibility)
        /// </summary>
        public static ExampleDatabaseClient CreateForPipeInsulation()
        {
            var client = new ExampleDatabaseClient("PipeInsulation", "PipeInsulationDB");
            return client;
        }

        /// <summary>
        /// Connects to the database using secure configuration
        /// </summary>
        public void Connect()
        {
            try
            {
                // Get connection string from the common secure configuration
                string connectionString = _connectionProvider.GetConnectionString();

                connection = new SqlConnection(connectionString);
                connection.Open();
                Console.WriteLine("Connection successful");
            }
            catch (SqlException e)
            {
                Console.WriteLine($"Error connecting to SQL Server: {e.Message}");
                connection = null;
                throw;
            }
            catch (Exception e)
            {
                Console.WriteLine($"Configuration error: {e.Message}");
                connection = null;
                throw;
            }
        }

        /// <summary>
        /// Example method showing how to execute queries with the secure connection
        /// </summary>
        /// <param name="tableName">Name of the table to query</param>
        /// <returns>DataTable with query results</returns>
        public DataTable GetAllRowsFromTable(string tableName)
        {
            if (connection == null || connection.State != ConnectionState.Open)
            {
                throw new InvalidOperationException("No connection established. Call Connect() first.");
            }

            try
            {
                using (SqlCommand command = new SqlCommand($"SELECT * FROM [{tableName}]", connection))
                {
                    using (SqlDataAdapter adapter = new SqlDataAdapter(command))
                    {
                        DataTable dataTable = new DataTable();
                        adapter.Fill(dataTable);
                        return dataTable;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error executing query: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Validates the current configuration
        /// </summary>
        /// <returns>True if configuration is valid and accessible</returns>
        public bool ValidateConfiguration()
        {
            return _connectionProvider.ValidateConfiguration();
        }

        /// <summary>
        /// Sets up configuration for the project
        /// </summary>
        /// <param name="server">Database server</param>
        /// <param name="database">Database name</param>
        /// <param name="username">Username</param>
        /// <param name="password">Password</param>
        /// <returns>True if configuration was created successfully</returns>
        public bool SetupConfiguration(string server, string database, string username, string password)
        {
            return _connectionProvider.CreateConfiguration(server, database, username, password);
        }

        /// <summary>
        /// Disposes of the database connection
        /// </summary>
        public void Dispose()
        {
            connection?.Close();
            connection?.Dispose();
            connection = null;
        }
    }

    /// <summary>
    /// Example showing migration from old PI_DatabaseClient to new common system
    /// </summary>
    public static class MigrationExample
    {
        /// <summary>
        /// Demonstrates how to migrate from old PI_SecureConfigManager to new common system
        /// </summary>
        public static void PerformMigration()
        {
            Console.WriteLine("Starting migration from old PI_SecureConfigManager...");

            try
            {
                // Step 1: Create backup of existing configuration
                string backupPath = ConfigurationMigrationUtility.CreateConfigBackup();
                if (backupPath != null)
                {
                    Console.WriteLine($"Backup created: {backupPath}");
                }

                // Step 2: Perform migration
                bool migrationSuccess = ConfigurationMigrationUtility.MigratePipeInsulationConfig(preserveOriginal: true);
                
                if (migrationSuccess)
                {
                    Console.WriteLine("Migration completed successfully");

                    // Step 3: Validate migration
                    bool validationSuccess = ConfigurationMigrationUtility.ValidateMigration();
                    
                    if (validationSuccess)
                    {
                        Console.WriteLine("Migration validation passed");
                        
                        // Step 4: Test with new system
                        var newClient = ExampleDatabaseClient.CreateForPipeInsulation();
                        if (newClient.ValidateConfiguration())
                        {
                            Console.WriteLine("New configuration system is working correctly");
                        }
                        else
                        {
                            Console.WriteLine("Warning: New configuration validation failed");
                        }
                    }
                    else
                    {
                        Console.WriteLine("Migration validation failed");
                        
                        // Restore from backup if validation fails
                        if (backupPath != null)
                        {
                            ConfigurationMigrationUtility.RestoreFromBackup(backupPath);
                            Console.WriteLine("Restored from backup due to validation failure");
                        }
                    }
                }
                else
                {
                    Console.WriteLine("Migration failed");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Migration error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of setting up configuration for a new project
        /// </summary>
        public static void SetupNewProject()
        {
            Console.WriteLine("Setting up configuration for new project...");

            try
            {
                // Create a client for a new project
                var client = new ExampleDatabaseClient("MyNewProject", "MyConnectionString");

                // Setup configuration
                bool setupSuccess = client.SetupConfiguration(
                    server: "myserver.database.windows.net",
                    database: "mydatabase",
                    username: "myuser",
                    password: "mypassword"
                );

                if (setupSuccess)
                {
                    Console.WriteLine("Configuration setup successful");

                    // Test the configuration
                    if (client.ValidateConfiguration())
                    {
                        Console.WriteLine("Configuration validation passed");
                        
                        // Test connection
                        client.Connect();
                        Console.WriteLine("Database connection test successful");
                    }
                    else
                    {
                        Console.WriteLine("Configuration validation failed");
                    }
                }
                else
                {
                    Console.WriteLine("Configuration setup failed");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Setup error: {ex.Message}");
            }
        }
    }
}
