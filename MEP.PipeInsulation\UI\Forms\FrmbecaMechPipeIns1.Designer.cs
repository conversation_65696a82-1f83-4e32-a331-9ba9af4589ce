﻿namespace MEP.PipeInsulation.UI.Forms
{
    partial class FrmbecaMechPipeIns1
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            tableLayoutPanel1 = new TableLayoutPanel();
            panel1 = new Panel();
            button1 = new Button();
            btn_Start = new Button();
            groupBox1 = new GroupBox();
            label1 = new Label();
            lblSelectedIndexRun = new Label();
            tableLayoutPanel1.SuspendLayout();
            panel1.SuspendLayout();
            groupBox1.SuspendLayout();
            SuspendLayout();
            // 
            // tableLayoutPanel1
            // 
            tableLayoutPanel1.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            tableLayoutPanel1.ColumnCount = 1;
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 23F));
            tableLayoutPanel1.Controls.Add(panel1, 0, 1);
            tableLayoutPanel1.Controls.Add(groupBox1, 0, 0);
            tableLayoutPanel1.Location = new Point(1, 54);
            tableLayoutPanel1.Margin = new Padding(4, 3, 4, 3);
            tableLayoutPanel1.Name = "tableLayoutPanel1";
            tableLayoutPanel1.RowCount = 2;
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 52F));
            tableLayoutPanel1.Size = new Size(620, 310);
            tableLayoutPanel1.TabIndex = 243;
            // 
            // panel1
            // 
            panel1.Controls.Add(button1);
            panel1.Controls.Add(btn_Start);
            panel1.Dock = DockStyle.Fill;
            panel1.Location = new Point(4, 261);
            panel1.Margin = new Padding(4, 3, 4, 3);
            panel1.Name = "panel1";
            panel1.Size = new Size(612, 46);
            panel1.TabIndex = 237;
            // 
            // button1
            // 
            button1.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            button1.BackColor = Color.FromArgb(18, 168, 178);
            button1.DialogResult = DialogResult.Cancel;
            button1.FlatAppearance.BorderColor = Color.FromArgb(18, 168, 178);
            button1.FlatStyle = FlatStyle.Flat;
            button1.Font = new Font("Arial", 10.2F, FontStyle.Regular, GraphicsUnit.Point, 0);
            button1.ForeColor = Color.White;
            button1.Location = new Point(301, 1);
            button1.Margin = new Padding(2);
            button1.Name = "button1";
            button1.Size = new Size(140, 44);
            button1.TabIndex = 229;
            button1.Text = "Cancel";
            button1.UseVisualStyleBackColor = false;
            // 
            // btn_Start
            // 
            btn_Start.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            btn_Start.BackColor = Color.FromArgb(255, 206, 0);
            btn_Start.DialogResult = DialogResult.OK;
            btn_Start.FlatAppearance.BorderColor = Color.FromArgb(255, 206, 0);
            btn_Start.FlatStyle = FlatStyle.Flat;
            btn_Start.Font = new Font("Arial", 10.2F, FontStyle.Bold, GraphicsUnit.Point, 0);
            btn_Start.ForeColor = Color.Black;
            btn_Start.Location = new Point(459, 2);
            btn_Start.Margin = new Padding(2);
            btn_Start.Name = "btn_Start";
            btn_Start.Size = new Size(140, 44);
            btn_Start.TabIndex = 230;
            btn_Start.Text = "START";
            btn_Start.UseVisualStyleBackColor = false;
            btn_Start.Click += btn_Start_Click;
            // 
            // groupBox1
            // 
            groupBox1.Controls.Add(label1);
            groupBox1.Controls.Add(lblSelectedIndexRun);
            groupBox1.Dock = DockStyle.Fill;
            groupBox1.Location = new Point(4, 3);
            groupBox1.Margin = new Padding(4, 3, 4, 3);
            groupBox1.Name = "groupBox1";
            groupBox1.Padding = new Padding(4, 3, 4, 3);
            groupBox1.Size = new Size(612, 252);
            groupBox1.TabIndex = 238;
            groupBox1.TabStop = false;
            // 
            // label1
            // 
            label1.Anchor = AnchorStyles.Top;
            label1.AutoSize = true;
            label1.Font = new Font("Arial", 9F, FontStyle.Bold, GraphicsUnit.Point, 0);
            label1.ForeColor = Color.Black;
            label1.Location = new Point(57, 67);
            label1.Margin = new Padding(4, 0, 4, 0);
            label1.Name = "label1";
            label1.Size = new Size(400, 135);
            label1.TabIndex = 224;
            label1.Text = "Pipes will be processed if they have the \r\nfollowing 'System Abbreviations';\r\n\r\n- HHW\r\n- CHW\r\n- DC\r\n- RF\r\n\r\nPipe with 'System Abbreviations' not listed here will not be processed";
            // 
            // lblSelectedIndexRun
            // 
            lblSelectedIndexRun.Anchor = AnchorStyles.Top;
            lblSelectedIndexRun.AutoSize = true;
            lblSelectedIndexRun.Font = new Font("Arial", 9F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lblSelectedIndexRun.ForeColor = Color.FromArgb(141, 14, 132);
            lblSelectedIndexRun.Location = new Point(57, 18);
            lblSelectedIndexRun.Margin = new Padding(4, 0, 4, 0);
            lblSelectedIndexRun.Name = "lblSelectedIndexRun";
            lblSelectedIndexRun.Size = new Size(458, 15);
            lblSelectedIndexRun.TabIndex = 223;
            lblSelectedIndexRun.Text = "This tool will automatically add insulation to the selected pipes and pipe fittings. ";
            // 
            // FrmbecaMechPipeIns1
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(627, 430);
            Controls.Add(tableLayoutPanel1);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            Margin = new Padding(5, 3, 5, 3);
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "FrmbecaMechPipeIns1";
            TitleText = "PIPE INSULATION PLACER | SELECTED";
            Controls.SetChildIndex(tableLayoutPanel1, 0);
            tableLayoutPanel1.ResumeLayout(false);
            panel1.ResumeLayout(false);
            groupBox1.ResumeLayout(false);
            groupBox1.PerformLayout();
            ResumeLayout(false);
        }

        #endregion

        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel1;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Button button1;
        private System.Windows.Forms.Button btn_Start;
        private System.Windows.Forms.GroupBox groupBox1;
        public System.Windows.Forms.Label label1;
        public System.Windows.Forms.Label lblSelectedIndexRun;
    }
}