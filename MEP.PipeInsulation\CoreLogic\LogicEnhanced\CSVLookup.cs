﻿using Autodesk.Revit.DB;
using MEP.PipeInsulation.Models;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CsvHelper;

namespace MEP.PipeInsulation.CoreLogic.LogicEnhanced
{
    public enum PI_DataSource
    {
        NZ_AU_Base,
        NZ_AU_Enhanced,
        SG,
        Custom
    }

    public enum PI_CNC
    {
        Commercial,
        NonCommercial
    }

    public class CSVLookup
    {
        //public Dictionary<string, List<PI_PipingSystem>> PreloadedData { get; private set; }

        public List<CSVRow> Rows { get; private set; }

        public CSVLookup(PI_DataSource dataSource, string dataFolderPath)
        {
            switch (dataSource)
            {
                case PI_DataSource.NZ_AU_Base:
                    Rows = ParseCSV(Path.Combine(dataFolderPath, "NZ_AU_Base.csv"));
                    break;
                case PI_DataSource.NZ_AU_Enhanced:
                    Rows = ParseCSV(Path.Combine(dataFolderPath, "NZ_AU_Enhanced.csv"));
                    break;
                case PI_DataSource.SG:
                    Rows = ParseCSV(Path.Combine(dataFolderPath, "SG.csv"));
                    break;
                case PI_DataSource.Custom:
                    Rows = ParseCSV(Path.Combine(dataFolderPath, "Custom.csv"));
                    break;
                default:
                    break;
            }

            //switch (dataSource)
            //{
            //    case PI_DataSource.NZ_AU_Base:
            //        PreloadedData = ParseAndGroupCSV(Path.Combine(dataFolderPath, "NZ_AU_Base"));
            //        break;
            //    case PI_DataSource.NZ_AU_Enhanced:
            //        PreloadedData = ParseAndGroupCSV(Path.Combine(dataFolderPath, "NZ_AU_Enhanced"));
            //        break;
            //    case PI_DataSource.SG:
            //        PreloadedData = ParseAndGroupCSV(Path.Combine(dataFolderPath, "SG"));
            //        break;
            //    case PI_DataSource.Custom:
            //        PreloadedData = ParseAndGroupCSV(Path.Combine(dataFolderPath, "Custom"));
            //        break;
            //    default:
            //        break;
            //}
        }

        private List<CSVRow> ParseCSV(string filePath)
        {
            using (var reader = new StreamReader(filePath))
            using (var csv = new CsvReader(reader))
            {
                return new List<CSVRow>(csv.GetRecords<CSVRow>());
            }
        }

        //private Dictionary<string, List<PI_PipingSystem>> ParseAndGroupCSV(string filePath)
        //{
        //    var groupedData = new Dictionary<string, List<PI_PipingSystem>>();

        //    using (var reader = new StreamReader(filePath))
        //    using (var csv = new CsvReader(reader))
        //    {
        //        // Load all rows into a list
        //        var rows = csv.GetRecords<CSVRow>().ToList();

        //        // Group rows by DesignLevel + Commercial/Non-Commercial for organization
        //        foreach (var designLevel in Enum.GetNames(typeof(DesignLevel)))
        //        {
        //            foreach (var commercialType in new[] { "Commercial", "Non-Commercial" })
        //            {
        //                string key = $"{designLevel}-{commercialType}";
        //                var filteredRows = rows.Where(row =>
        //                    row.DesignLevel.Equals(designLevel, StringComparison.OrdinalIgnoreCase) &&
        //                    row.CNC.Equals(commercialType, StringComparison.OrdinalIgnoreCase)).ToList();

        //                // Group rows by PipeSystem
        //                var pipingSystems = filteredRows
        //                    .GroupBy(row => row.PipeSystem)
        //                    .Select(group => new PI_PipingSystem(group.Key, new List<Element>(), group.ToList()))
        //                    .ToList();

        //                if (!groupedData.ContainsKey(key))
        //                    groupedData[key] = new List<PI_PipingSystem>();

        //                groupedData[key].AddRange(pipingSystems);
        //            }
        //        }
        //    }

        //    return groupedData;

        //}

        //// Fetch preloaded data by DesignLevel and Commercial/Non-Commercial
        //public List<PI_PipingSystem> GetPipingSystems(string designLevel, string commercialType)
        //{
        //    string key = $"{designLevel}-{commercialType}";

        //    if (PreloadedData.ContainsKey(key))
        //        return PreloadedData[key];

        //    return new List<PI_PipingSystem>();
        //}
    }
}
