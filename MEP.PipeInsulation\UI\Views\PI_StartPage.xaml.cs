﻿using Autodesk.Revit.DB;
using MEP.PipeInsulation.CoreLogic;
using MEP.PipeInsulation.CoreLogic.LogicEnhanced;
using MEP.PipeInsulation.UI.Views.DataSources;
using MEP.PipeInsulation.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace MEP.PipeInsulation.UI.Views
{
    /// <summary>
    /// Interaction logic for PI_MainPage.xaml
    /// </summary>
    public partial class PI_StartPage : Page
    {
        PI_MainViewModel _viewModel;

        public bool IsSelectElementsFromRevitChecked
        {
            get { return rb_SelectElementsFromRevit.IsChecked == true; }
        }


        public PI_StartPage(PI_MainViewModel viewModel)
        {
            InitializeComponent();

            _viewModel = viewModel;
            DataContext = _viewModel;

            rb_SelectSystemByName.IsChecked = true;

        }
          
        private void rb_SelectSystemByName_Checked(object sender, RoutedEventArgs e)
        {
            ClearSelectedSystem();
        }

        private void rb_SelectSystemFromRevit_Checked(object sender, RoutedEventArgs e)
        {

            SetSystemControlsEnabled(false);
            ClearSelectedSystem();
        }

        private void rb_SelectSystemFromRevit_Unchecked(object sender, RoutedEventArgs e)
        {
            SetSystemControlsEnabled(rb_SelectSystemFromRevit.IsChecked != true);
        }

        private void rb_SelectElementsFromRevit_Checked(object sender, RoutedEventArgs e)
        {
            SetSystemControlsEnabled(false);
            ClearSelectedSystem();
        }

        private void rb_SelectElementsFromRevit_Unchecked(object sender, RoutedEventArgs e)
        {
            SetSystemControlsEnabled(rb_SelectElementsFromRevit.IsChecked != true);
        }

        private void SetSystemControlsEnabled(bool isEnabled)
        {
            tb_SearchSystem.IsEnabled = isEnabled;
            tv_Systems.IsEnabled = isEnabled;
        }

        private void ClearSelectedSystem()
        {
            foreach (var ps in _viewModel.FilteredPipeSystems.Where(ps => ps.IsSelected))
            {
                ps.IsSelected = false;
            }
        }

        private void btn_ViewNZAU_Base_Click(object sender, RoutedEventArgs e)
        {
            NavigationService.Navigate(new PI_DataSourceView(_viewModel));
        }

        private void btn_ViewNZAU_Enhanced_Click(object sender, RoutedEventArgs e)
        {
            NavigationService.Navigate(new PI_DataSourceView(_viewModel));
        }

        private void btn_ViewSG_Click(object sender, RoutedEventArgs e)
        {
            NavigationService.Navigate(new PI_DataSourceView(_viewModel));
        }

        private void btn_EditCustom_Click(object sender, RoutedEventArgs e)
        {
            NavigationService.Navigate(new PI_DataSourceView(_viewModel));
        }

        private void btn_Close_Click(object sender, RoutedEventArgs e)
        {
            var window = Window.GetWindow(this);
            if (window != null) window.Close();
        }

        private void btn_Info_ViewNZAU_Base_Click(object sender, RoutedEventArgs e)
        {
            NZAU_BaseEnhancedInfo();
        }

        private void btn_Info_ViewNZAU_Enhanced_Click(object sender, RoutedEventArgs e)
        {
            NZAU_BaseEnhancedInfo();
        }

        private void NZAU_BaseEnhancedInfo()
        {
            System.Windows.Forms.MessageBox.Show("Base and Enhanced options apply to circulating DHW systems only." +
                "\nCheck with P&D spec writer which applies to project.",
                                        "NZAU Base Enhanced Info",
                                        MessageBoxButtons.OK,
                                        MessageBoxIcon.Information);
        }
    }
}
