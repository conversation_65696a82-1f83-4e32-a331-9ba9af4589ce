﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using Autodesk.Revit.UI.Selection;
using BecaActivityLogger.CoreLogic.Data;
using BecaRevitUtilities;
using BecaRevitUtilities.ElementUtilities;
using BecaRevitUtilities.Extensions;

//using BecaTelemetryHandler;
using MEP.PipeInsulation.CoreLogic;
using MEP.PipeInsulation.Models;

//using Microsoft.ApplicationInsights.Channel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using TaskDialog = Autodesk.Revit.UI.TaskDialog;

namespace MEP.PipeInsulation.UI.ModelessRevitForm
{
    #region Modeless Form Handler 

    public class ModelessPipeInsulationHandler
    {
        #region Fields

        static ModelessPipeInsulation _frmModelessPipeInsulation;

        #endregion

        #region Properties

        public static ModelessPipeInsulation FrmModelessSelection { get => _frmModelessPipeInsulation; }

        #endregion

        #region Methods
        private static void ClearSelection(UIDocument activeUIDocument)
        {
            activeUIDocument.Selection.SetElementIds(new List<ElementId>());
        }

        public static void SelectElementsInModel(UIApplication uiapp, BecaActivityLoggerData logger)
        {
            //try
            //{
            //    var selectedPI_Elements = new List<PI_Element>();

            //    IList<Reference> refs = uiapp.ActiveUIDocument.Selection.PickObjects(ObjectType.Element, "Please select elements");
            //    refs.Select(eleID => uiapp.ActiveUIDocument.Document.GetElement(eleID)).Where(x => (BuiltInCategory)x.Category.Id.IntegerValue == BuiltInCategory.OST_PipeFitting || (BuiltInCategory)x.Category.Id.IntegerValue == BuiltInCategory.OST_PipeCurves).ToList()
            //        .ForEach(e => selectedPI_Elements.Add(new PI_Element(_frmModelessPipeInsulation.Doc, e, _frmModelessPipeInsulation.View3D)));

            //    _frmModelessPipeInsulation.SelectedPipesAndFittings = selectedPI_Elements;
            //    logger.Log($"{selectedPI_Elements.Count} elements selected in model.", LogType.Information);
            //    //telemetry.LogProcessing(new Dictionary<string, string>() { { "Elements selected", $"{selectedPI_Elements.Count} elements." } });
            //}
            //catch (Exception e)
            //{
            //    _frmModelessPipeInsulation.SelectedPipesAndFittings = null;
            //    logger.Log($"Error from selection in model: {e.Message}", LogType.Error);
            //    //telemetry.LogException(e, "SelectElementsInModel", null, null);
            //}

        }

        public static void SelectOneElementInModel(UIApplication uiapp, BecaActivityLoggerData logger)
        {
            try
            {
                var reference = uiapp.ActiveUIDocument.Selection.PickObject(ObjectType.Element, "Please select a pipe or a fitting from a system");
                if (reference != null)
                {
                    var el = uiapp.ActiveUIDocument.Document.GetElement(reference.ElementId);
                    if (((BuiltInCategory)el.Category.Id.IntegerValue() == BuiltInCategory.OST_PipeFitting || (BuiltInCategory)el.Category.Id.IntegerValue() == BuiltInCategory.OST_PipeCurves) && el != null)
                        _frmModelessPipeInsulation.SelectedElementInSystem = el;
                    else
                    {
                        System.Windows.Forms.MessageBox.Show("Please select a pipe or fitting");
                        _frmModelessPipeInsulation.SelectedElementInSystem = null;
                    }
                }
                else
                {
                    _frmModelessPipeInsulation.SelectedElementInSystem = null;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                _frmModelessPipeInsulation.SelectedElementInSystem = null;
                logger.Log($"Error from selection single element in model: {ex.Message}", LogType.Error);
                //telemetry.LogException(ex, "SelectOneElementInModel", null, null);
            }

        }

        public static void Create3DViewHandler(UIApplication uiapp, BecaActivityLoggerData logger)
        {
            View3D systemView = null;
            var viewName = $"PipeInsulationView_{uiapp.Application.Username}";
            try
            {
                using (var t = new Transaction(uiapp.ActiveUIDocument.Document, "Create 3D Pipe Insulation View"))
                {
                    t.Start();
                    systemView = PipeInsulationHelper.Create3DSystemView(uiapp, _frmModelessPipeInsulation, viewName);
                    t.Commit();
                }

                logger.Log($"3D View: {viewName} created.", LogType.Information);
            }
            catch (Exception e)
            {
                logger.Log($"Error from Creating 3D Pipe Insulation View: {e.Message}", LogType.Error);
                //telemetry.LogException(e, "PipeInsulationHelper.Create3DSystemView", null, null);
            }

            uiapp.ActiveUIDocument.ActiveView = systemView;

            try
            {
                using (var t = new Transaction(uiapp.ActiveUIDocument.Document, "Set view detail level"))
                {
                    t.Start();
                    var selectedDuctsAndFittingsId = _frmModelessPipeInsulation.SelectedPipesAndFittings.Select(x => x.Element.Id).ToList();
                    uiapp.ActiveUIDocument.ShowElements(selectedDuctsAndFittingsId);
                    systemView.DetailLevel = ViewDetailLevel.Fine;

                    // Isolate elements
                    systemView.IsolateElementsTemporary(selectedDuctsAndFittingsId);

                    t.Commit();
                }

                logger.Log("View detail level set.", LogType.Information);
            }
            catch (Exception e)
            {
                logger.Log($"Error from Create3DViewHandler: {e.Message}", LogType.Error);
                //telemetry.LogException(e, "Create3DViewHandler", null, null);
            }

        }

        public static void RemoveAllInsulations(BecaActivityLoggerData logger)
        {
            try
            {
                new InsulationPlacementLogic(_frmModelessPipeInsulation.Uiapp.ActiveUIDocument.Document).RemoveAllInsulations(_frmModelessPipeInsulation.SelectedPipesAndFittings);
                _frmModelessPipeInsulation.dgv_SelectedELements.DataSource = null;
                _frmModelessPipeInsulation.BringToFront();

                logger.Log("All insulation successfully removed.", LogType.Information);
            }
            catch (Exception e)
            {
                logger.Log($"Error from removing all insulations: {e.Message}", LogType.Error);
                //telemetry.LogException(e, "RemoveAllInsulations", null, null);
            }
        }

        public static void AddInsulations(BecaActivityLoggerData logger)
        {
            // Check for locked elements in worksharing environment
            var lockedElements = 0;
            var lockedOwners = new List<string>();
            var sb = new StringBuilder();

            try
            {
                var doc = _frmModelessPipeInsulation.SelectedPipesAndFittings.FirstOrDefault().Element.Document;

                foreach (var e in _frmModelessPipeInsulation.SelectedPipesAndFittings.Select(e => e.Element))
                {
                    if (e.IsLocked(doc))
                    {
                        lockedElements++;
                        if (!lockedOwners.Contains(e.ElementOwner(doc)))
                            lockedOwners.Add(e.ElementOwner(doc));
                    }
                }

                // Run pipe insulation logic
                if (lockedElements > 0)
                {
                    lockedOwners.ForEach(o => sb.AppendLine(o));
                    DialogResult dr = MessageBox.Show($"Warning:\nThe current selection includes {lockedElements}\nelements locked out by:\n\n{sb}\n\nDo you want to continue and ignore the\nlocked elements?",
                          "Locked elements found", MessageBoxButtons.YesNo);
                    switch (dr)
                    {
                        case DialogResult.Yes:
                            RunInsulationPlacementLogic(logger);
                            break;
                        case DialogResult.No:
                            break;
                    }
                }
                else
                {
                    RunInsulationPlacementLogic(logger);
                }

                logger.Log("Process end.", LogType.Information);
            }
            catch (Exception e)
            {
                logger.Log($"Error from removing all insulations: {e.Message}", LogType.Error);
                //telemetry.LogException(e, "AddInsulations", null, null);
            }

        }

        private static void SetThicknessOfSelectedElementsUsingWorstCase(List<PI_Element> elements, List<PI_Database> database, BecaActivityLoggerData logger)
        {
            try
            {
                foreach (var element in elements)
                {
                    if (element.SystemType != null)
                    {
                        var matchingDatabase = database.FirstOrDefault(db =>
                        db.PipeType == element.SystemType.Name &&
                        element.Diameter >= db.MinDiameter &&
                        element.Diameter <= db.MaxDiameter
                        );

                        if (matchingDatabase != null)
                        {
                            var thicknessValues = element.IsInterior
                                ? new double[]
                                {
                            matchingDatabase.InteriorElastometric,
                            matchingDatabase.InteriorPolyethelene,
                            matchingDatabase.InteriorPhenolic
                                }
                                : new double[]
                                {
                            matchingDatabase.ExteriorElastometric,
                            matchingDatabase.ExteriorPolyethelene,
                            matchingDatabase.ExteriorPhenolic
                                };

                            element.Thickness = RevitUnitConvertor.MmToInternal(thicknessValues.Max());
                        }
                    }
                }
            }
            catch (Exception e)
            {
                logger.Log($"Error from SetThicknessOfSelectedElementsUsingWorstCase: {e.Message}", LogType.Error);
                //telemetry.LogException(e, "SetThicknessOfSelectedElementsUsingWorstCase", null, null);
            }
        }

        private static void SetThicknessOfSelectedElementsCustomSettings(List<PI_Element> elements, List<PI_ProjectSettings> customSettings, 
            List<PI_Database> customDatabase, BecaActivityLoggerData logger)
        {
            try
            {
                foreach (var element in elements)
                {
                    // Set InsulationType for each PI_Elements from settings
                    element.InsulationTypeToSet = customSettings.SelectMany(item => item.InsulationSettings)
                    .FirstOrDefault()?.InsulationType;

                    // Find thickness by comparing to custom database
                    var matchingDatabase = customDatabase.FirstOrDefault(db =>
                        db.PipeType == element.SystemType.Name &&
                        element.Diameter >= db.MinDiameter &&
                        element.Diameter <= db.MaxDiameter
                    );

                    if (matchingDatabase != null)
                    {
                        Dictionary<string, double> thicknessDictionary = new Dictionary<string, double>();
                        if (element.IsInterior)
                        {
                            thicknessDictionary.Add(InsulationType.Elastometric.ToString(), matchingDatabase.InteriorElastometric);
                            thicknessDictionary.Add(InsulationType.Polyethylene.ToString(), matchingDatabase.InteriorPolyethelene);
                            thicknessDictionary.Add(InsulationType.Phenolic.ToString(), matchingDatabase.InteriorPhenolic);
                        }
                        else
                        {
                            thicknessDictionary.Add(InsulationType.Elastometric.ToString(), matchingDatabase.ExteriorElastometric);
                            thicknessDictionary.Add(InsulationType.Polyethylene.ToString(), matchingDatabase.ExteriorPolyethelene);
                            thicknessDictionary.Add(InsulationType.Phenolic.ToString(), matchingDatabase.ExteriorPhenolic);
                        }

                        if (element.InsulationTypeToSet.ToString().Contains("Beca"))
                            element.Thickness = RevitUnitConvertor.MmToInternal(thicknessDictionary.Values.Max());
                        else
                            element.Thickness = RevitUnitConvertor.MmToInternal(thicknessDictionary[element.InsulationTypeToSet]);
                    }
                }
            }
            catch (Exception e)
            {
                logger.Log($"Error from SetThicknessOfSelectedElementsCustomSettings: {e.Message}", LogType.Error);
                //telemetry.LogException(e, "SetThicknessOfSelectedElementsCustomSettings", null, null);
            }
        }

        private static void RunInsulationPlacementLogic(BecaActivityLoggerData logger)
        {
            // Set SelectedPipesAndFittings thickness property based on database and settings lookup
            if (_frmModelessPipeInsulation.SelectedCountryReference == CountryReference.Custom.ToString())
                SetThicknessOfSelectedElementsCustomSettings(_frmModelessPipeInsulation.SelectedPipesAndFittings,
                    _frmModelessPipeInsulation.ProjectSettingsToRun, _frmModelessPipeInsulation.CustomDatabase, logger);
            else
                SetThicknessOfSelectedElementsUsingWorstCase(_frmModelessPipeInsulation.SelectedPipesAndFittings,
                    _frmModelessPipeInsulation.SelectedDatabase, logger);
            try
            {
                using (var trans = new Transaction(_frmModelessPipeInsulation.Doc, "Add Insulation"))
                {
                    trans.Start();
                    var appliedCount = 0;
                    int nCount = _frmModelessPipeInsulation.SelectedPipesAndFittings.Count;
                    string progressMessage = "{0} of " + nCount.ToString() + " elements processed...";
                    string caption = "Applying Insulations";
                    using (var pf = new Common.UI.Forms.BecaProgressForm(caption, progressMessage, nCount))
                    {
                        foreach (var pI_Element in _frmModelessPipeInsulation.SelectedPipesAndFittings)
                        {
                            try
                            {
                                Autodesk.Revit.DB.Plumbing.PipeInsulation.Create(_frmModelessPipeInsulation.Doc,
                                pI_Element.Element.Id, _frmModelessPipeInsulation.SelectedInsulationType.Id, pI_Element.Thickness);

                                appliedCount++;
                                pf.Increment();
                            }
                            catch (Exception)
                            {

                            }

                        }
                    }
                    trans.Commit();

                    var successMessage = $"Applied insulations to\n{appliedCount} elements from {_frmModelessPipeInsulation.SelectedPipesAndFittings.Count} elements";
                    logger.Log(successMessage, LogType.Information);
                    //telemetry.LogProcessing(new Dictionary<string, string>() { { "Applied Insulations", $"{appliedCount} elements applied." } });
                    TaskDialog.Show("Result", successMessage);
                }
            }
            catch (Exception e)
            {
                TaskDialog.Show("Error", e.Message);
                logger.Log($"Error from RunInsulationPlacementLogic: {e.Message}", LogType.Error);
                //telemetry.LogException(e, "RunInsulationPlacementLogic", null, null);
            }
        }

        public static void ChangeTypes(UIApplication uiapp)
        {
            //int failedTypes = 0;
            //int changedTypes = 0;
            //var doc = uiapp.ActiveUIDocument.Document;
            //using (var t = new Transaction(doc, "Change Types"))
            //{
            //    t.Start();
            //    try
            //    {
            //        foreach (var dict in _frmModelessPipeInsulation.TypeMapping)
            //        {
            //            doc.GetElement(dict.Key).ChangeTypeId(dict.Value);
            //            changedTypes++;
            //        }
            //    }
            //    catch (Exception e)
            //    {
            //        failedTypes++;
            //        System.Windows.Forms.MessageBox.Show(e.Message);
            //    }

            //    t.Commit();

            //    // Refresh duct and fitting list
            //    _frmModelessPipeInsulation.DuctsAndFittings.Clear();
            //    _frmModelessPipeInsulation.FilterDuctsAndFittings();

            //    if (failedTypes == 0)
            //        System.Windows.Forms.MessageBox.Show("Selected types has been changed.\nReselect system to see the changes.");
            //    else if (changedTypes > 0)
            //        System.Windows.Forms.MessageBox.Show("Some of the selected types has been changed.\nReselect system to see the changes.");
            //}
        }

        /// <summary>
        ///   This method creates and shows a modeless dialog, unless it already exists.
        /// </summary>
        /// <remarks>
        ///   The external command invokes this on the end-user's request
        /// </remarks>
        /// 
        public static void ShowForm(UIApplication uiapp, BecaActivityLoggerData logger, View3D view3D)
        {
            // If we do not have a dialog yet, create and show it
            if (_frmModelessPipeInsulation == null || _frmModelessPipeInsulation.IsDisposed)
            {

                // A new handler to handle request posting by the dialog
                RequestHandler handler = new RequestHandler(logger);

                // External Event for the dialog to use (to post requests)
                ExternalEvent exEvent = ExternalEvent.Create(handler);

                // We give the objects to the new dialog;
                // The dialog becomes the owner responsible fore disposing them, eventually.
                _frmModelessPipeInsulation = new ModelessPipeInsulation(exEvent, handler, logger, uiapp, view3D);
                _frmModelessPipeInsulation.Show();
            }
            else
                _frmModelessPipeInsulation.Activate();
            //m_MyForm.BringToFront();
        }

        /// <summary>
        ///   Waking up the dialog from its waiting state.
        /// </summary>
        /// 
        public static void WakeFormUp()
        {
            if (_frmModelessPipeInsulation != null)
            {
                _frmModelessPipeInsulation.WakeUp();
            }
        }

        /// <summary>
        /// Must be called in OnShutdown(UIControlledApplication a) Event of the App command.
        /// </summary>
        public static void OnRevitShutDown()
        {
            if (_frmModelessPipeInsulation != null)
            {
                _frmModelessPipeInsulation.Close();
            }
        }

        #endregion

    }

    #endregion
}
