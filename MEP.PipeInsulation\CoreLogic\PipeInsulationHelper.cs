﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Plumbing;
using Autodesk.Revit.UI;
using BecaRevitUtilities;
using BecaRevitUtilities.RevitViewsUtilities;
using Common.UI.Forms;
using MEP.PipeInsulation.CoreLogic.LogicEnhanced;
using MEP.PipeInsulation.Models;
using MEP.PipeInsulation.UI.ModelessRevitForm;
using MEP.PipeInsulation.ViewModels;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Color = Autodesk.Revit.DB.Color;

namespace MEP.PipeInsulation.CoreLogic
{
    public class PipeInsulationHelper
    {
        /// <summary>
        /// This is used for processing ray projection to check for element is inside or outside
        /// by checking all 6 sides of boundaries
        /// </summary>
        public static View3D InitializePI3DView(UIApplication uiapp)
        {
            View3D view3D = null;

            using (var t = new Transaction(uiapp.ActiveUIDocument.Document, "Create PI_3DView"))
            {
                t.Start();
                // Create a 3D view to use for the ReferenceIntersector constructor
                view3D = Create3DView(uiapp.ActiveUIDocument.Document);
                t.Commit();
            }

            return view3D;
        }

        private static View3D Create3DView(Document doc)
        {
            string viewName = "PI_3DView";
            if (doc == null || doc.IsFamilyDocument)
                return null;

            var views3D = new FilteredElementCollector(doc)
                .OfClass(typeof(View3D)).Cast<View3D>();
            var existedView = views3D.FirstOrDefault(view => !view.IsTemplate &&
            view.Name.Equals(viewName));
            if (existedView != null)
                return existedView;
            else
            {
                View3D view3D = null;
                var collector = new FilteredElementCollector(doc);
                var viewFamilyType = collector.OfClass(typeof(ViewFamilyType)).Cast<ViewFamilyType>()
                  .FirstOrDefault(x => x.ViewFamily == ViewFamily.ThreeDimensional);

                view3D = View3D.CreateIsometric(doc, viewFamilyType.Id);
                view3D.Name = viewName;

                return view3D;
            }
        }

        public static View3D Create3DSystemView(UIApplication uiapp, ModelessPipeInsulation frmModelessPipeInsulation, string viewName)
        {
            var selectedDuctsAndFittingsId = frmModelessPipeInsulation.SelectedPipesAndFittings.Select(x => x.Element.Id).ToList();
            var otherElements = new List<Element>();
            var categories = new List<BuiltInCategory>();
            categories.Add(BuiltInCategory.OST_FlexPipeCurves);
            categories.Add(BuiltInCategory.OST_PipeAccessory);
            categories.Add(BuiltInCategory.OST_MechanicalEquipment);
            var categoryFilter = new ElementMulticategoryFilter(categories);

            foreach (var category in categories)
            {
                new FilteredElementCollector(uiapp.ActiveUIDocument.Document).WherePasses(categoryFilter).WhereElementIsNotElementType().ToList().ForEach(x => otherElements.Add(x));
            }

            var otherElementIdsInSelectedSystems = new List<ElementId>();
            foreach (var element in otherElements)
            {
                foreach (var systemName in frmModelessPipeInsulation.SelectedSystemNames)
                {
                    if (element.get_Parameter(BuiltInParameter.RBS_SYSTEM_NAME_PARAM)?.AsString() == systemName)
                    {
                        otherElementIdsInSelectedSystems.Add(element.Id);
                    }
                }
            }

            View3D view3D = null;
            view3D = Create3DViewAndIsolateElements(uiapp.ActiveUIDocument.Document, uiapp.ActiveUIDocument, viewName, selectedDuctsAndFittingsId, otherElementIdsInSelectedSystems);

            return view3D;

        }

        public static View3D Create3DSystemViewWPF(UIApplication uiapp, PI_MainViewModel viewModel, string viewName)
        {
            var selectedDuctsAndFittingsId = GetSelectedElementIds(viewModel);

            var otherElements = new List<Element>();
            var categories = new List<BuiltInCategory>
            {
                BuiltInCategory.OST_FlexPipeCurves,
                BuiltInCategory.OST_PipeAccessory,
                BuiltInCategory.OST_MechanicalEquipment
            };
            var categoryFilter = new ElementMulticategoryFilter(categories);

            foreach (var category in categories)
            {
                new FilteredElementCollector(uiapp.ActiveUIDocument.Document).WherePasses(categoryFilter).WhereElementIsNotElementType().ToList().ForEach(x => otherElements.Add(x));
            }

            // Build a HashSet for quick lookup of system names
            var systemNames = new HashSet<string>(viewModel.FilteredPipeSystems.Select(s => s.Name));

            var otherElementIdsInSelectedSystems = otherElements
                .Where(element =>
                    systemNames.Contains(
                        element.get_Parameter(BuiltInParameter.RBS_SYSTEM_NAME_PARAM)?.AsString()))
                .Select(element => element.Id)
                .ToList();

            View3D view3D = null;
            view3D = Create3DViewAndIsolateElements(uiapp.ActiveUIDocument.Document, uiapp.ActiveUIDocument, viewName, selectedDuctsAndFittingsId, otherElementIdsInSelectedSystems);

            return view3D;

        }

        public static List<ElementId> GetSelectedElementIds(PI_MainViewModel viewModel)
        {
            var selectedDuctsAndFittingsId = new HashSet<ElementId>();
            if (viewModel.ManuallySelectedElements != null)
            {
                selectedDuctsAndFittingsId.UnionWith(
                    viewModel.ManuallySelectedElements
                        .Where(pI_Element => pI_Element.Element != null)
                        .Select(pI_Element => pI_Element.Element.Id)
                );
            }
            if (viewModel.AllFilteredElements != null)
            {
                selectedDuctsAndFittingsId.UnionWith(
                    viewModel.AllFilteredElements
                        .Where(pI_Element => pI_Element.Element != null)
                        .Select(pI_Element => pI_Element.Element.Id)
                );
            }

            return selectedDuctsAndFittingsId.ToList();
        }

        public static View3D Create3DViewAndIsolateElements(Document doc, UIDocument uidoc, string viewName, List<ElementId> elementIdsToProcess, List<ElementId> otherElementIdsInSelectedSystems)
        {
            // Check view with the same name, if open close it
            var currentView = ViewUtility.GetViewByName(doc, viewName) as Autodesk.Revit.DB.View;
            if (currentView?.Id == doc.ActiveView.Id)
                CloseView(uidoc, currentView);

            // If view name exists delete it
            if (ViewNameExist(doc, viewName))
                doc.Delete(ViewUtility.GetViewByName(doc, viewName).Id);

            // Create isometric view
            var viewFamilyType = new FilteredElementCollector(doc).OfClass(typeof(ViewFamilyType)).Cast<ViewFamilyType>()
              .FirstOrDefault(x => x.ViewFamily == ViewFamily.ThreeDimensional);
            var view3D = View3D.CreateIsometric(doc, viewFamilyType.Id);
            view3D.Name = viewName;
            
            // Set Visual Style to Shaded = 4 and scale 1:20
            view3D.get_Parameter(BuiltInParameter.MODEL_GRAPHICS_STYLE).Set(4);
            view3D.Scale = 20;
            
            // Override graphics for other elements in system
            Element solidFill = new FilteredElementCollector(doc).OfClass(typeof(FillPatternElement)).Where(q => q.Name.Contains("Solid")).First();
            OverrideGraphicSettings ogs_noFlowIds = SetOverrideGraphicSettings(new Color(137, 137, 137), new Color(255, 255, 255), solidFill, true);  // Gray transparent
            foreach (var id in otherElementIdsInSelectedSystems)
            {
                view3D.SetElementOverrides(id, ogs_noFlowIds);
            }

            return view3D;
        }

        private static void CloseView(UIDocument uidoc, Autodesk.Revit.DB.View viewToClose)
        {
            uidoc.RequestViewChange(viewToClose);
            IList<UIView> lViews = uidoc.GetOpenUIViews();
            foreach (UIView pView in lViews)
            {
                if (pView.ViewId == viewToClose.Id)
                    pView.Close();
            }
        }

        private static bool ViewNameExist(Autodesk.Revit.DB.Document doc, string name)
        {
            IEnumerable<Element> viewCreated = new FilteredElementCollector(doc).OfClass(typeof(View3D)).Where(x => x.Name == name);
            if (viewCreated.Count() == 0)
                return false;
            else
                return true;
        }

        private static OverrideGraphicSettings SetOverrideGraphicSettings(Color lineColor, Color surfaceColorA, Element solidFill, bool setTransparency)
        {
            OverrideGraphicSettings ogs_ElementA = new OverrideGraphicSettings();
            ogs_ElementA.SetProjectionLineColor(lineColor);
            ogs_ElementA.SetProjectionLineWeight(1);

#if TargetYear2014 || TargetYear2015 || TargetYear2016 || TargetYear2017 || TargetYear2018
            ogs_ElementA.SetProjectionFillColor(surfaceColorA);
#else
            ogs_ElementA.SetSurfaceForegroundPatternId(solidFill.Id);
            ogs_ElementA.SetSurfaceForegroundPatternColor(surfaceColorA);
#endif
            if (setTransparency)
                ogs_ElementA.SetSurfaceTransparency(60);

            return ogs_ElementA;
        }

        public static void RestAllInterior(ObservableCollection<PI_Element> allFilteredElements)
        {
            var elementCount = allFilteredElements.Count;
            string progressMessage = "{0} of " + elementCount.ToString() + " pipe elements processed...";
            string caption = $"Checking whether elements are interior or exterior.";
            using (BecaProgressForm pf = new BecaProgressForm(caption, progressMessage, elementCount))
            {
                foreach (var pI_Element in allFilteredElements)
                {
                    pI_Element.IsInterior = true;
                    pf.Increment();
                }
            }
        }

        //Find Insulation Thickness
        public static double GetInsulationThickness(string subStrSysAbbr, double subDiameter)
        {
            double subInsThickness;

            switch (subStrSysAbbr)
            {
                case "CHW":
                    if (subDiameter < 15)
                    {
                        subInsThickness = 0;
                    }
                    else
                    {
                        if (subDiameter < 66)
                        {
                            subInsThickness = 32;
                        }
                        else
                        {
                            if (subDiameter < 251)
                            {
                                subInsThickness = 38;
                            }
                            else
                            { subInsThickness = 44; }
                        }
                    }
                    break;

                case "HHW":
                    if (subDiameter < 15)
                    {
                        subInsThickness = 0;
                    }
                    else
                    {
                        if (subDiameter < 63)
                        {
                            subInsThickness = 25;
                        }
                        else
                        {
                            if (subDiameter < 126)
                            {
                                subInsThickness = 38;
                            }
                            else
                            {
                                if (subDiameter < 201)
                                {
                                    subInsThickness = 50;
                                }
                                else
                                { subInsThickness = 63; }
                            }
                        }
                    }
                    break;

                case "DC ":
                    subInsThickness = 9;
                    break;

                case "RF ":
                    if (subDiameter < 15)
                    {
                        subInsThickness = 0;
                    }
                    else
                    {
                        subInsThickness = 32;
                    }
                    break;

                default:
                    subInsThickness = 0;
                    break;
            }
            return subInsThickness;
        }
    }
}
