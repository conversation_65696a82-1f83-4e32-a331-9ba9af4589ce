﻿using Autodesk.Revit.DB;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PipeInsulation.CoreLogic.InteriorExteriorDetection
{
    public class LinkManager
    {
        private readonly Document _document;

        public LinkManager(Document document)
        {
            _document = document;
        }

        /// <summary>
        /// Retrieves loaded Revit links from the active document.
        /// </summary>
        public List<RevitLinkInstance> GetLoadedLinks()
        {
            return new FilteredElementCollector(_document)
                .OfClass(typeof(RevitLinkInstance))
                .WhereElementIsNotElementType()
                .Cast<RevitLinkInstance>()
                .Where(linkInstance =>
                {
                    if (linkInstance == null) return false;

                    var typeId = linkInstance.GetTypeId();
                    if (typeId == null || typeId == ElementId.InvalidElementId) return false;

                    var linkType = _document.GetElement(typeId) as RevitLinkType;
                    return linkType != null && RevitLinkType.IsLoaded(_document, linkType.Id);
                })
                .ToList();
        }
    }
}
