﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Configuration;
using System.Windows.Forms;
using Polly;
using Polly.Wrap;
using System.Collections;
using System.Threading.Tasks;
//#if TargetYear2025
using Microsoft.Data.SqlClient;
//#else
//using System.Data.SqlClient;
//#endif

namespace BecaAzureSQL
{
    /// <summary>
    /// This class provides a higher-level abstraction for interacting with a SQL database.
    /// </summary>
    public class SqlControllerAsync : SqlControllerBase
    {
        private readonly AsyncPolicyWrap _policyWrap;

        public SqlControllerAsync(ILogger logger) : base (logger)
        {
            _policyWrap = Policy.WrapAsync(CreateRetryPolicyAsync(), CreateCircuitBreakerPolicyAsync());
        }

        private IAsyncPolicy CreateRetryPolicyAsync()
        {
            return Policy
                .Handle<SqlException>(ex => IsExecutionTimeout(ex))
                .WaitAndRetryAsync(new[]
                {
                    // Wait times between retries (1 sec, 2 secs, 4 secs)
                    TimeSpan.FromSeconds(1),
                    TimeSpan.FromSeconds(2),
                    TimeSpan.FromSeconds(4)
                }, onRetry: (exception, timespan, retryCount, context) =>
                {
                    _logger.Log($"Retry {retryCount} of {context.PolicyKey} at {context.OperationKey}: due to {exception}.");
                });
        }

        private IAsyncPolicy CreateCircuitBreakerPolicyAsync()
        {
            return Policy
            .Handle<SqlException>(ex => IsExecutionTimeout(ex))
            .CircuitBreakerAsync(
                exceptionsAllowedBeforeBreaking: 5,
                durationOfBreak: TimeSpan.FromMinutes(1),
                onBreak: (exception, breakDelay) =>
                {
                    _logger.Log($"Circuit broken due to {exception}. Breaking for {breakDelay.TotalSeconds} seconds.");
                },
                onReset: () => _logger.Log("Circuit reset."),
                onHalfOpen: () => _logger.Log("Circuit half-open, next call is a trial.")
            );
        }

        /// <summary>
        /// Executes a non-query SQL command and returns the number of affected rows.
        /// </summary>
        /// <param name="query"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        public async Task<int> ExecuteNonQueryAsync(string query, Dictionary<string, object> parameters = null)
        {
            // Create a new SqlConnection using the DatabaseConnection instance.
            using (SqlConnection connection = _databaseConnection.CreateConnection())
            {
                // Create a SqlCommand using the SqlCommandBuilder and the provided query.
                using (SqlCommand command = _commandBuilder.CreateCommand(connection, query))
                {
                    // Add parameters to the SqlCommand using the local AddParameters method.
                    AddParameters(command, parameters);

                    // Asynchronously open the connection.
                    await connection.OpenAsync();

                    try
                    {
                        // Execute the non-query command asynchronously and return the number of affected rows.
                        return await command.ExecuteNonQueryAsync();
                    }
                    catch (Exception e)
                    {
                        _logger.Log(e.ToString());
                        return 0;
                    }
                }
            }
        }

        public async Task ExecuteNonQueryFromTSQLStatementAsync(string mergeStatement)
        {
            if (string.IsNullOrEmpty(mergeStatement))
            {
                return;
            }

            try
            {
                await _policyWrap.ExecuteAsync(async (context) =>
                {
                    using (var connection = _databaseConnection.CreateConnection())
                    {
                        await connection.OpenAsync();

                        using (var command = new SqlCommand(mergeStatement, connection))
                        {
                            command.CommandTimeout = 180; // Timeout in seconds, 3 minutes
                            await command.ExecuteNonQueryAsync();
                        }
                    }
                }, new Polly.Context("ExecuteNonQueryFromTSQLStatementAsync"));
            }
            catch(Exception e)
            {
                Console.WriteLine(e);
            }
            
        }

        public async Task InsertRecordAsync(string tableName, Dictionary<string, object> data)
        {
            string columns = string.Join(", ", data.Keys.Select(key => $"[{key}]"));
            string values = string.Join(", ", data.Values.Select(value => $"'{value}'")); // TODO: consider SQL injection protection

            string query = $"INSERT INTO {tableName} ({columns}) VALUES ({values});";

            // TODO: makesure that the operation key is unique or descriptive enough for logging or debugging 
            try {
                await _policyWrap.ExecuteAsync(async context =>
                {
                    await ExecuteNonQueryAsync(query, data);
                }, new Polly.Context($"InsertRecordAsync_{tableName}"));
            }
            catch(Exception e)
            {
                Console.WriteLine(e);
            }
            
        }

        public async Task UpdateRecordAsync(string tableName, Dictionary<string, object> data, string whereClause, Dictionary<string, object> parameters = null)
        {
            string setClause = string.Join(", ", data.Keys.Select(key => $"{key} = @{key}"));
            string query = $"UPDATE {tableName} SET {setClause} WHERE {whereClause};";

            try
            {
                await _policyWrap.ExecuteAsync(async context =>
                {
                    await ExecuteNonQueryAsync(query, MergeDictionaries(data, parameters));
                }, new Polly.Context($"UpdateRecordAsync_{tableName}"));
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
        }

        public async Task DeleteRecordAsync(string tableName, string whereClause, Dictionary<string, object> parameters = null)
        {
            string query = $"DELETE FROM {tableName} WHERE {whereClause};";
            try
            {
                // Use _policyWrap to execute the operation asynchronously with defined policies
                await _policyWrap.ExecuteAsync(async context =>
                {
                    await ExecuteNonQueryAsync(query, parameters);
                }, new Polly.Context($"DeleteRecordAsync_{tableName}"));
            }
            catch(Exception e)
            {
                Console.WriteLine(e);
            }
        }

        public async Task UpsertRecordAsync(string tableName, Dictionary<string, object> data, string whereClause)
        {
            // Check if the value exists with the given where clause
            bool valueExists = await DoesValueExistCompositeKeyChecksAsync(tableName, whereClause, data);

            if (valueExists)
            {
                // If the value exists, update the record
                await UpdateRecordAsync(tableName, data, whereClause);
            }
            else
            {
                // If the value does not exist, insert a new record
                await InsertRecordAsync(tableName, data);
            }
        }

        /// <summary>
        /// Retrieves a dictionary of Time and Status values from the DL_SyncEvents table where BUID and Time match the specified criteria.
        /// </summary>
        /// <param name="buid">The BUID to filter by.</param>
        /// <param name="timeCriteria">The Time value to filter by.</param>
        /// <returns>A dictionary with Time as keys and Status as values.</returns>
        public Dictionary<string, string> GetTimeAndStatusByBuidAndTime(string buid, string timeCriteria)
        {
            Dictionary<string, string> timeStatusMap = new Dictionary<string, string>();

            // Define the SQL query to select Time and Status values based on BUID and Time criteria.
            string query = @"SELECT [Time], [Status] FROM [DL_SyncEvents] WHERE [BUID] = @Buid AND [Time] = @TimeCriteria";

            // Create a dictionary for parameters to prevent SQL injection.
            var parameters = new Dictionary<string, object>
            {
                { "@Buid", buid },
                { "@TimeCriteria", timeCriteria }
            };

            // Execute the query and collect the results.
            using (SqlConnection connection = _databaseConnection.CreateConnection())
            {
                using (SqlCommand command = CreateCommand(connection, query))
                {
                    AddParameters(command, parameters);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            string time = reader["Time"] != DBNull.Value ? reader["Time"].ToString() : null;
                            string status = reader["Status"] != DBNull.Value ? reader["Status"].ToString() : null;

                            // Ensure the key (Time) is not null or empty before adding to the dictionary.
                            if (!string.IsNullOrEmpty(time) && !timeStatusMap.ContainsKey(time))
                            {
                                timeStatusMap.Add(time, status);
                            }
                        }
                    }
                }
            }

            return timeStatusMap;
        }

        /// <summary>
        /// Merges two dictionaries and returns the result.
        /// </summary>
        /// <param name="first"></param>
        /// <param name="second"></param>
        /// <returns></returns>
        private Dictionary<string, object> MergeDictionaries(Dictionary<string, object> first, Dictionary<string, object> second)
        {
            if (first == null) return second;
            if (second == null) return first;

            var result = new Dictionary<string, object>(first);
            foreach (var kvp in second)
            {
                result[kvp.Key] = kvp.Value;
            }
            return result;
        }

        public async Task<bool> DoesValueExistAsync(string tableName, string columnA, object valueA)
        {
            string query = $"SELECT COUNT(*) FROM {tableName} WHERE {columnA} = @{columnA};";
            int count = await ExecuteQueryAsync(query, columnA, valueA);
            return count > 0;
        }

        public async Task<bool> DoesValueExistCompositeKeyChecksAsync(string tableName, string whereClause, Dictionary<string, object> parameters)
        {
            string query = $"SELECT COUNT(*) FROM {tableName} WHERE {whereClause};";
            int count = await ExecuteScalarAsync<int>(query, parameters);
            return count > 0;
        }

        public async Task<bool> DoesValueExistWithConditionAsync(string tableName, string columnA, object valueA, string columnB, object valueB)
        {
            // Build the SELECT query to check if the value exists with the given condition
            string query = $"SELECT COUNT(*) FROM {tableName} WHERE {columnA} = @{columnA} AND {columnB} = @{columnB};";

            int count = await ExecuteQueryWithConditionAsync(query, columnA, valueA, columnB, valueB);

            return count > 0;
        }

        public DateTime? GetLatestDateForBUID(string tableName, string columnDate, string columnBUID, object bUIDValue)
        {
            // Build the SELECT query to get the latest date for the given BUID
            string query = $"SELECT MAX([{columnDate}]) FROM {tableName} WHERE [{columnBUID}] = @BUIDValue;";

            // Create parameters for the query
            var parameters = new Dictionary<string, object>
            {
                { "@BUIDValue", bUIDValue }
            };

            // Execute the scalar query using the ExecuteScalar<T> method
            object result = ExecuteScalarAsync<object>(query, parameters);

            // Check if the result is not null or DBNull.Value, and convert it to DateTime
            if (result != null && result != DBNull.Value)
            {
                return Convert.ToDateTime(result);
            }

            // Return null if no date is found
            return null;
        }

        private async Task<int> ExecuteQueryAsync(string query, string columnA, object valueA)
        {
            var parameters = new Dictionary<string, object>
            {
                { columnA, valueA }
            };

            return await ExecuteScalarAsync<int>(query, parameters);
        }

        private async Task<int> ExecuteQueryWithConditionAsync(string query, string columnA, object valueA, string columnB, object valueB)
        {
            // Create parameters
            var parameters = new Dictionary<string, object>
            {
                { columnA, valueA },
                { columnB, valueB }
            };

            // Execute the query and get the result
            return await ExecuteScalarAsync<int>(query, parameters);
        }

        public Dictionary<string, string> GetGUIDAndCreatedDateDictionary(string tableName, string columnA, object valueA, string columnB, object valueB, string columnC)
        {
            // Build the SELECT query to check if the value exists with the given condition
            string query = $"SELECT {columnB}, {columnC} FROM {tableName} WHERE {columnA} = @{columnA} AND {columnB} = @{columnB};";

            // Create parameters
            var parameters = new Dictionary<string, object>
            {
                { columnA, valueA },
                { columnB, valueB }
            };

            // Execute the query and get the result
            Dictionary<string, string> resultDictionary = ExecuteScalarDictionary(query, parameters);

            return resultDictionary;
        }

        /// <summary>
        /// Executes a scalar query and returns the result of the specified type.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="query"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        public async Task<T> ExecuteScalarAsync<T>(string query, Dictionary<string, object> parameters)
        {
            return await _policyWrap.ExecuteAsync(async context =>
            {
                using (var connection = _databaseConnection.CreateConnection())
                {
                    await connection.OpenAsync();
                    using (var command = _commandBuilder.CreateCommand(connection, query))
                    {
                        // Assuming AddParameters is a method to add parameters to the command
                        _commandBuilder.AddParameters(command, parameters);
                        object result = await command.ExecuteScalarAsync();
                        return (result == DBNull.Value || result == null) ? default : (T)result;
                    }
                }
            }, new Polly.Context("ExecuteScalar"));
        }

        private Dictionary<string, string> ExecuteScalarDictionary(string query, Dictionary<string, object> parameters = null)
        {
            // Create a new SqlConnection using the DatabaseConnection instance.
            using (SqlConnection connection = _databaseConnection.CreateConnection())
            {
                // Create a SqlCommand using the SqlCommandBuilder and the provided query.
                using (SqlCommand command = _commandBuilder.CreateCommand(connection, query))
                {
                    _commandBuilder.AddParameters(command, parameters);

                    connection.Open();

                    // Execute the query and get the SqlDataReader
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        // Check if there are any rows
                        if (reader.Read())
                        {
                            // Create a dictionary with the specified columns
                            Dictionary<string, string> resultDictionary = new Dictionary<string, string>
                            {
                                { reader[0].ToString(), reader[1].ToString() }
                            };

                            return resultDictionary;
                        }
                        else
                        {
                            // No rows found, return an empty dictionary
                            return new Dictionary<string, string>();
                        }
                    }
                }
            }
        }
    }
}
