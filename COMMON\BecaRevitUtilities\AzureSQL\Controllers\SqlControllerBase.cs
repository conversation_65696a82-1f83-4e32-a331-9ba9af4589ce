﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
//#if TargetYear2025
using Microsoft.Data.SqlClient;

//#else
//using System.Data.SqlClient;
//#endif

namespace BecaAzureSQL
{
    /// <summary>
    /// Abstract base class for SQL controllers, providing shared functionality across synchronous and asynchronous database operations.
    /// This class encapsulates common database operations such as command creation, parameter addition, and execution timeout checks. 
    /// It serves as a common foundation for both synchronous and asynchronous derived classes.
    /// </summary>
    public abstract class SqlControllerBase
    {
        protected readonly string _connectionString;
        protected readonly DatabaseConnection _databaseConnection;
        protected readonly SqlCommandBuilder _commandBuilder;
        protected readonly ILogger _logger;

        protected SqlControllerBase(ILogger logger, string catalogue = "DevaBot")
        {
            _connectionString = GetConnectionString(catalogue);
            _databaseConnection = new DatabaseConnection(_connectionString);
            _commandBuilder = new SqlCommandBuilder();
            _logger = logger;
        }

        /// <summary>
        /// Gets the secure database connection string for the specified catalogue
        /// This method replaces the hardcoded connection string with secure configuration
        /// </summary>
        /// <param name="catalogue">Database catalogue name</param>
        /// <returns>Secure connection string</returns>
        protected virtual string GetConnectionString(string catalogue = "DevaBot")
        {
            try
            {
                // Try to get from secure configuration first
                string configFileName = $"BecaAzureSQL_{catalogue}.config";
                string secureConnectionString = GetSecureConnectionString(configFileName, catalogue);

                if (!string.IsNullOrEmpty(secureConnectionString))
                {
                    return secureConnectionString;
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to get secure connection string: {ex.Message}");
            }

            // Fallback to app.config or default (for backward compatibility during transition)
            try
            {
                var connectionString = System.Configuration.ConfigurationManager.ConnectionStrings[$"BecaAzureSQL_{catalogue}"]?.ConnectionString;
                if (!string.IsNullOrEmpty(connectionString))
                {
                    return connectionString;
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to get connection string from app.config: {ex.Message}");
            }

            // Final fallback (temporary - should be removed after migration)
            _logger?.Log("Warning: Using fallback connection string. Please configure secure connection string.");
            return $"Data Source=devabotdbserver.database.windows.net;Initial Catalog={catalogue};User ID=devabotadmin;Password=*********;";
        }

        /// <summary>
        /// Gets secure connection string from encrypted configuration file
        /// This method implements the same security approach as the common SecureConfigManager
        /// </summary>
        /// <param name="configFileName">Name of the configuration file</param>
        /// <param name="catalogue">Database catalogue name</param>
        /// <returns>Decrypted connection string or null if not found</returns>
        private string GetSecureConnectionString(string configFileName, string catalogue)
        {
            try
            {
                string configPath = GetConfigFilePath(configFileName);
                if (System.IO.File.Exists(configPath))
                {
                    string encryptedContent = System.IO.File.ReadAllText(configPath);
                    string encryptionKey = GetCompanyEncryptionKey();
                    return DecryptString(encryptedContent, encryptionKey);
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error reading secure configuration: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Gets the path to the configuration file
        /// </summary>
        /// <param name="configFileName">Name of the configuration file</param>
        private string GetConfigFilePath(string configFileName)
        {
            string assemblyLocation = System.Reflection.Assembly.GetExecutingAssembly().Location;
            string assemblyDirectory = System.IO.Path.GetDirectoryName(assemblyLocation);
            return System.IO.Path.Combine(assemblyDirectory, configFileName);
        }

        /// <summary>
        /// Gets the company-wide encryption key using dynamic generation
        /// This uses the same enhanced security approach as the updated PI_SecureConfigManager
        /// </summary>
        private string GetCompanyEncryptionKey()
        {
            string companyKey = GenerateCompanyKey();

            using (var sha256 = System.Security.Cryptography.SHA256.Create())
            {
                byte[] hash = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(companyKey));
                return Convert.ToBase64String(hash);
            }
        }

        /// <summary>
        /// Generates the company key algorithmically to avoid hardcoding
        /// This uses the same enhanced algorithm as the updated PI_SecureConfigManager
        /// </summary>
        private string GenerateCompanyKey()
        {
            // Enhanced keygen algorithm - same as implemented in PI_SecureConfigManager
            int[] seeds = { 2100, 2500, 2200, 1800, 2700, 2400, 2300, 2600, 2000, 1900,
                           2800, 2150, 1950, 2350, 2050, 2450, 1850, 2750, 2250, 2550,
                           2650, 1750, 2850, 2950, 1650, 2175, 2375, 2575, 2775, 2975,
                           1575, 2125 };

            StringBuilder sb = new StringBuilder();

            // Use multiple transformation layers to make reverse engineering harder
            for (int i = 0; i < seeds.Length; i++)
            {
                int seed = seeds[i];

                // Apply multiple mathematical transformations
                int transform1 = (seed / 25) ^ 42;
                int transform2 = (transform1 + i * 7) % 126;
                int transform3 = transform2 < 32 ? transform2 + 65 : transform2;

                // Ensure we get printable ASCII characters
                if (transform3 > 126) transform3 = (transform3 % 95) + 32;
                if (transform3 < 32) transform3 += 32;

                sb.Append((char)transform3);
            }

            return sb.ToString();
        }

        /// <summary>
        /// Decrypts a string using AES decryption
        /// This uses the same decryption approach as the PI_SecureConfigManager
        /// </summary>
        private string DecryptString(string cipherText, string key)
        {
            byte[] keyBytes = Convert.FromBase64String(key);
            byte[] cipherBytes = Convert.FromBase64String(cipherText);

            using (var aes = System.Security.Cryptography.Aes.Create())
            {
                aes.Key = keyBytes;

                // Extract IV from the beginning of the cipher text
                byte[] iv = new byte[aes.BlockSize / 8];
                Array.Copy(cipherBytes, 0, iv, 0, iv.Length);
                aes.IV = iv;

                using (var decryptor = aes.CreateDecryptor())
                using (var msDecrypt = new System.IO.MemoryStream(cipherBytes, iv.Length, cipherBytes.Length - iv.Length))
                using (var csDecrypt = new System.Security.Cryptography.CryptoStream(msDecrypt, decryptor, System.Security.Cryptography.CryptoStreamMode.Read))
                using (var srDecrypt = new System.IO.StreamReader(csDecrypt))
                {
                    return srDecrypt.ReadToEnd();
                }
            }
        }





        /// <summary>
        /// Creates and configures a SqlCommand with the specified SqlConnection, query, and command type.
        /// </summary>
        /// <param name="connection"></param>
        /// <param name="query"></param>
        /// <param name="commandType"></param>
        /// <returns></returns>
        protected SqlCommand CreateCommand(SqlConnection connection, string query, CommandType commandType = CommandType.Text)
        {
            SqlCommand command = connection.CreateCommand();
            command.CommandText = query;
            command.CommandType = commandType;
            return command;
        }

        /// <summary>
        /// Adds parameters to the given SqlCommand based on the provided parameter dictionary.
        /// </summary>
        /// <param name="command"></param>
        /// <param name="parameters"></param>
        protected void AddParameters(SqlCommand command, Dictionary<string, object> parameters)
        {
            if (parameters != null)
            {
                foreach (var parameter in parameters)
                {
                    command.Parameters.AddWithValue(parameter.Key, parameter.Value ?? DBNull.Value);
                }
            }
        }

        protected bool IsExecutionTimeout(SqlException ex)
        {
            // Check for the SQL Server timeout exception.
            // For Azure SQL Database, -2 is typically used for client-side timeouts.
            return ex.Message.Contains("timeout period elapsed") || ex.Number == -2;
        }
    }
}
