using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
//#if TargetYear2025
using Microsoft.Data.SqlClient;

//#else
//using System.Data.SqlClient;
//#endif

namespace BecaAzureSQL
{
    /// <summary>
    /// Abstract base class for SQL controllers, providing shared functionality across synchronous and asynchronous database operations.
    /// This class encapsulates common database operations such as command creation, parameter addition, and execution timeout checks. 
    /// It serves as a common foundation for both synchronous and asynchronous derived classes.
    /// </summary>
    public abstract class SqlControllerBase
    {
        protected readonly string _connectionString;
        protected readonly DatabaseConnection _databaseConnection;
        protected readonly SqlCommandBuilder _commandBuilder;
        protected readonly ILogger _logger;

        protected SqlControllerBase(ILogger logger, string catalogue = "DevaBot")
        {
            _connectionString = $"Data Source=devabotdbserver.database.windows.net;Initial Catalog={catalogue};User ID=devabotadmin;Password=*********;";
            _databaseConnection = new DatabaseConnection(_connectionString);
            _commandBuilder = new SqlCommandBuilder();
            _logger = logger;
        }





        /// <summary>
        /// Creates and configures a SqlCommand with the specified SqlConnection, query, and command type.
        /// </summary>
        /// <param name="connection"></param>
        /// <param name="query"></param>
        /// <param name="commandType"></param>
        /// <returns></returns>
        protected SqlCommand CreateCommand(SqlConnection connection, string query, CommandType commandType = CommandType.Text)
        {
            SqlCommand command = connection.CreateCommand();
            command.CommandText = query;
            command.CommandType = commandType;
            return command;
        }

        /// <summary>
        /// Adds parameters to the given SqlCommand based on the provided parameter dictionary.
        /// </summary>
        /// <param name="command"></param>
        /// <param name="parameters"></param>
        protected void AddParameters(SqlCommand command, Dictionary<string, object> parameters)
        {
            if (parameters != null)
            {
                foreach (var parameter in parameters)
                {
                    command.Parameters.AddWithValue(parameter.Key, parameter.Value ?? DBNull.Value);
                }
            }
        }

        protected bool IsExecutionTimeout(SqlException ex)
        {
            // Check for the SQL Server timeout exception.
            // For Azure SQL Database, -2 is typically used for client-side timeouts.
            return ex.Message.Contains("timeout period elapsed") || ex.Number == -2;
        }
    }
}
