﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Configuration;
using System.Windows.Forms;
using Polly.Wrap;
using Polly;
using Common.UI.Forms;
using Autodesk.Revit.DB.Structure;
//#if TargetYear2025
using Microsoft.Data.SqlClient;
//#else
//using System.Data.SqlClient;
//#endif

namespace BecaAzureSQL
{
    /// <summary>
    /// This class provides a higher-level abstraction for interacting with a SQL database.
    /// </summary>
    public class SqlController : SqlControllerBase
    {
        private readonly PolicyWrap _policyWrap;


        public SqlController(ILogger logger, string catalogue = "DevaBot") : base(logger, catalogue) 
        {
            _policyWrap = Policy.Wrap(CreateRetryPolicy(), CreateCircuitBreakerPolicy());
        }


        private ISyncPolicy CreateRetryPolicy()
        {
            return Policy
                .Handle<SqlException>(ex => IsExecutionTimeout(ex))
                .Retry(
                    // Number of retry attempts
                    3,
                    onRetry: (exception, retryCount) => // Removed the context parameter
                    {
                        _logger.Log($"Retry {retryCount}: due to {exception}.");
                    });
        }

        private ISyncPolicy CreateCircuitBreakerPolicy()
        {
            return Policy
                .Handle<SqlException>(ex => IsExecutionTimeout(ex))
                .CircuitBreaker(
                    exceptionsAllowedBeforeBreaking: 5,
                    durationOfBreak: TimeSpan.FromMinutes(1),
                    onBreak: (exception, breakDelay) =>
                    {
                        _logger.Log($"Circuit broken due to {exception}. Breaking for {breakDelay.TotalSeconds} seconds.");
                    },
                    onReset: () => _logger.Log("Circuit reset."),
                    onHalfOpen: () => _logger.Log("Circuit half-open, next call is a trial.")
                );
        }

        /// <summary>
        /// Executes a non-query SQL command and returns the number of affected rows.
        /// </summary>
        /// <param name="query"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        public int ExecuteNonQuery(string query, Dictionary<string, object> parameters = null)
        {
            // Create a new SqlConnection using the DatabaseConnection instance.
            using (SqlConnection connection = _databaseConnection.CreateConnection())
            {
                // Create a SqlCommand using the SqlCommandBuilder and the provided query.
                using (SqlCommand command = _commandBuilder.CreateCommand(connection, query))
                {
                    // Add parameters to the SqlCommand using the local AddParameters method.
                    AddParameters(command, parameters);

                    connection.Open();

                    try
                    {
                        // Execute the non-query command and return the number of affected rows.
                        return command.ExecuteNonQuery();
                    }
                    catch (Exception e)
                    {
                        return 0;
                    }
                }
            }
        }

        public string ExecuteNonQueryFromTSQLStatement(string mergeStatement)
        {
            
            if (string.IsNullOrEmpty(mergeStatement))
            {
                return "Merge Statement didn't exist.";
            }

            string message = "";
            try {
                _policyWrap.Execute(() =>
                {
                    using (var connection = _databaseConnection.CreateConnection())
                    {
                        connection.Open();

                        using (SqlCommand command = new SqlCommand(mergeStatement, connection))
                        {
                            command.CommandTimeout = 300; // Timeout in seconds, 180 is 3 minutes
                            int rowsAffected = command.ExecuteNonQuery();
                            message = "Rows Affected: " + rowsAffected;
                        }
                    }
                });
            }
            catch (Exception e)
            {
               message = e.ToString();
            }
            return message;
            
        }

        public void ExecuteNonQueryFromTSQLStatementWithCounter(string tempTableCreation, string bulkInsert, string mergeQuery, int chunkSize, int rowCount, BecaProgressForm2 pbMain, out string errorMessage, out int affectedRows)
        {
            errorMessage = string.Empty;
            affectedRows = 0;
            if (string.IsNullOrEmpty(tempTableCreation) || string.IsNullOrEmpty(bulkInsert) || string.IsNullOrEmpty(mergeQuery) || chunkSize <= 0)
            {
                return;
            }

            int parsedAffectedRows = 0;
            try
            {
                _policyWrap.Execute(() =>
                {
                    using (var connection = _databaseConnection.CreateConnection())
                    {
                        connection.Open();
                        int rowsAffected;
                        int totalRowsAffected = 0;

                        // Create the temporary table
                        using (SqlCommand command = new SqlCommand(tempTableCreation, connection))
                        {
                            command.ExecuteNonQuery();
                        }

                        // Insert data into the temporary table in batches
                        using (SqlCommand bulkCommand = new SqlCommand(bulkInsert, connection))
                        {
                            bulkCommand.CommandTimeout = 180; // Timeout in seconds, 180 is 3 minutes
                            SqlParameter chunkSizeParam = bulkCommand.Parameters.Add("@chunkSize", SqlDbType.Int);
                            chunkSizeParam.Value = chunkSize;

                            do
                            {
                                bulkCommand.CommandText = bulkInsert + $" WHERE id NOT IN (SELECT TOP (@chunkSize) id FROM #TempProgressOvertime ORDER BY id)";
                                rowsAffected = bulkCommand.ExecuteNonQuery();
                                totalRowsAffected += rowsAffected;
                                pbMain.IncrementPB2($"Processing progress overtime export. Rows processed: {totalRowsAffected} of {rowCount}.");
                            } while (rowsAffected == chunkSize);
                        }

                        // Execute the MERGE statement
                        using (SqlCommand mergeCommand = new SqlCommand(mergeQuery, connection))
                        {
                            mergeCommand.CommandTimeout = 180; // Timeout in seconds, 180 is 3 minutes
                            rowsAffected = mergeCommand.ExecuteNonQuery();
                            totalRowsAffected += rowsAffected;
                        }

                        // Drop the temporary table
                        using (SqlCommand dropCommand = new SqlCommand("DROP TABLE #TempProgressOvertime;", connection))
                        {
                            dropCommand.ExecuteNonQuery();
                        }

                        int.TryParse(totalRowsAffected.ToString(), out parsedAffectedRows);
                        Console.WriteLine($"Total rows affected: {parsedAffectedRows}");
                    }
                });

                affectedRows = parsedAffectedRows;
            }
            catch (Exception e)
            {
                errorMessage = e.ToString();
            }
        }

        public int InsertRecord(string tableName, Dictionary<string, object> data)
        {
            string columns = string.Join(", ", data.Keys.Select(key => $"[{key}]"));
            string values = string.Join(", ", data.Keys.Select(key => "@" + key));

            string query = $"INSERT INTO {tableName} ({columns}) VALUES ({values});";

            try 
            {
               return _policyWrap.Execute(() => { return ExecuteNonQuery(query, data); });
            }
            catch(Exception e)
            {
                Console.WriteLine(e);
            }
            return 0;
        }

        public int BulkInsertWithSqlBulkCopy(string tableName, List<Dictionary<string, object>> dataList)
        {
            if (dataList == null || dataList.Count == 0)
                return 0;

            var dt = new DataTable();
            foreach (var key in dataList[0].Keys)
                dt.Columns.Add(key);

            foreach (var dict in dataList)
                dt.Rows.Add(dict.Values.ToArray());

            try
            {
                _policyWrap.Execute(() =>
                {
                    using (var connection = new SqlConnection(_connectionString))
                    {
                        connection.Open();
                        using (var bulkCopy = new SqlBulkCopy(connection))
                        {
                            bulkCopy.DestinationTableName = tableName;
                            bulkCopy.WriteToServer(dt);
                        }
                    }
                });
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                return 0;
            }

            return dataList.Count;
        }

        public int UpdateRecord(string tableName, Dictionary<string, object> data, string whereClause, Dictionary<string, object> parameters = null)
        {
            string setClause = string.Join(", ", data.Keys.Select(key => $"{key} = @{key}"));
            string query = $"UPDATE {tableName} SET {setClause} WHERE {whereClause};";

            try {
                _policyWrap.Execute(() =>
                {
                    return ExecuteNonQuery(query, MergeDictionaries(data, parameters));
                });
            }catch(Exception e)
            {
                Console.WriteLine(e);
                
            }
            return 0;

        }

        public void DeleteRecord(string tableName, string whereClause, Dictionary<string, object> parameters = null)
        {
            string query = $"DELETE FROM {tableName} WHERE {whereClause};";
            try { 
                _policyWrap.Execute(() => ExecuteNonQuery(query, parameters));
            }
            catch(Exception e)
            {
                Console.WriteLine(e);
            }
        }

        public int UpsertRecord(string tableName, Dictionary<string, object> data, string whereClause)
        {
            // Check if the value exists with the given where clause
            bool valueExists = DoesValueExistCompositeKeyChecks(tableName, whereClause, data);

            if (valueExists)
            {
                // If the value exists, update the record
                return UpdateRecord(tableName, data, whereClause);
            }
            else
            {
                // If the value does not exist, insert a new record
                return InsertRecord(tableName, data);
            }
        }

        /// <summary>
        /// Retrieves a dictionary of Time and Status values from the DL_SyncEvents table where BUID and Time match the specified criteria.
        /// </summary>
        /// <param name="buid">The BUID to filter by.</param>
        /// <param name="timeCriteria">The Time value to filter by.</param>
        /// <returns>A dictionary with Time as keys and Status as values.</returns>
        public Dictionary<string, string> GetTimeAndStatusByBuidAndTime(string buid, string timeCriteria)
        {
            Dictionary<string, string> timeStatusMap = new Dictionary<string, string>();

            // Define the SQL query to select Time and Status values based on BUID and Time criteria.
            string query = @"SELECT [Time], [Status] FROM [DL_SyncEvents] WHERE [BUID] = @Buid AND [Date] = @TimeCriteria";

            // Create a dictionary for parameters to prevent SQL injection.
            var parameters = new Dictionary<string, object>
            {
                { "@Buid", buid },
                { "@TimeCriteria", timeCriteria }
            };

            // Execute the query and collect the results.
            using (SqlConnection connection = _databaseConnection.CreateConnection())
            {
                using (SqlCommand command = CreateCommand(connection, query))
                {
                    AddParameters(command, parameters);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            string time = reader["Time"] != DBNull.Value ? reader["Time"].ToString() : null;
                            string status = reader["Status"] != DBNull.Value ? reader["Status"].ToString() : null;

                            // Ensure the key (Time) is not null or empty before adding to the dictionary.
                            if (!string.IsNullOrEmpty(time) && !timeStatusMap.ContainsKey(time))
                            {
                                timeStatusMap.Add(time, status);
                            }
                        }
                    }
                }
            }

            return timeStatusMap;
        }

        /// <summary>
        /// Merges two dictionaries and returns the result.
        /// </summary>
        /// <param name="first"></param>
        /// <param name="second"></param>
        /// <returns></returns>
        private Dictionary<string, object> MergeDictionaries(Dictionary<string, object> first, Dictionary<string, object> second)
        {
            if (first == null) return second;
            if (second == null) return first;

            var result = new Dictionary<string, object>(first);
            foreach (var kvp in second)
            {
                result[kvp.Key] = kvp.Value;
            }
            return result;
        }

        public bool DoesValueExist(string tableName, string columnA, object valueA)
        {
            // Query to check if the value exists with the given condition
            string query = $"SELECT COUNT(*) FROM {tableName} WHERE {columnA} = @{columnA};";

            return ExecuteQuery(query, columnA, valueA) > 0;
        }

        public int GetOpenCount(string tableName, string columnA, object valueA)
        {
            // Query to check if the value exists with the given condition
            string query = $"SELECT OpenCount FROM {tableName} WHERE {columnA} = @{columnA};";

            return ExecuteQuery(query, columnA, valueA);
        }

        public bool IsBuidEnabled(string tableName, string buidColumn, string isEnabledColumn, string buidValue)
        {
            // Query to check if the BUID exists and is enabled
            string query = $"SELECT COUNT(*) FROM {tableName} WHERE {buidColumn} = @{buidColumn} AND {isEnabledColumn} = 1;";

            // Execute the query and get the result as a DataTable
            DataTable dt = ExecuteQuery(query, new Dictionary<string, object>
            {
                { buidColumn, buidValue }
            });

            // Check if DataTable has at least one row and then convert the first cell value to int
            if (dt != null && dt.Rows.Count > 0)
            {
                // Assuming the count is in the first column of the first row
                int count = Convert.ToInt32(dt.Rows[0][0]);
                return count > 0;
            }
            return false;
        }

        public bool IsInitialTableGood(string revitVersion, string bUIDColumn, string bUID, string revitModelGUID, string centralPathString, string modelName)
        {
            string currentDate = DateTime.UtcNow.ToString("dd/MM/yyyy");

            if (DoesValueExist("DL_BUID_DICT", bUIDColumn, bUID))
            {
                int openCount = GetOpenCount("DL_BUID_DICT", bUIDColumn, bUID);
                openCount += 1;
                //if value exists, we want to update the OpenCount as OpenCount +1, and add the last
                string whereClause = "BUID = @BUID";
                var data = new Dictionary<string, object>
                {
                        { "BUID", bUID },
                        { "UpdatedDate", currentDate},
                        { "OpenCount", openCount} 
                };
                UpdateRecord("DL_BUID_DICT", data, whereClause);
                return true;
            }
            else
            {
                //else we can just add a new date for both updated, and 
                try
                {
                    Dictionary<string, object> insertData = new Dictionary<string, object>
                    {
                        { bUIDColumn, bUID },
                        { "RevitModelGUID", revitModelGUID },
                        { "RevitModelFilename", centralPathString },
                        { "Modelname", modelName },
                        { "IsEnabled", 0 },
                        { "RevitVersion", revitVersion },
                        { "AddedDate", currentDate},
                        { "UpdatedDate", currentDate},
                        { "OpenCount", 1}
                    };
                    InsertRecord("DL_BUID_DICT", insertData);

                    return true;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        /// <summary>
        /// Executes a SQL query and returns the result as a DataTable.
        /// </summary>
        /// <param name="query"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        public DataTable ExecuteQuery(string query, Dictionary<string, object> parameters = null)
        {
            // Create a new SqlConnection using the DatabaseConnection instance.
            using (SqlConnection connection = _databaseConnection.CreateConnection())
            {
                // Create a SqlCommand using the SqlCommandBuilder and the provided query.
                using (SqlCommand command = _commandBuilder.CreateCommand(connection, query))
                {
                    // Add parameters to the SqlCommand using the SqlCommandBuilder.
                    _commandBuilder.AddParameters(command, parameters);

                    connection.Open();

                    // Execute the query and load the result into a DataTable.
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        DataTable dataTable = new DataTable();
                        dataTable.Load(reader);
                        return dataTable;
                    }
                }
            }
        }

        public bool SetBuidEnabledStatus(string tableName, string buidColumn, string buidValue, string isEnabledColumn, bool isEnabled)
        {
            // Convert the boolean isEnabled to an integer value that SQL Server can understand (1 for true, 0 for false)
            int isEnabledValue = isEnabled ? 1 : 0;

            // Query to change the IsEnabled status based on the BUID value
            string query = $"UPDATE {tableName} SET {isEnabledColumn} = @IsEnabledValue WHERE {buidColumn} = @{buidColumn};";

            // Assuming ExecuteNonQuery is a method that executes a SQL command (like an UPDATE statement)
            // and returns the number of rows affected. This method should be implemented to prevent SQL injection
            // by using parameterized queries.
            int rowsAffected = ExecuteNonQuery(query, new Dictionary<string, object>
            {
                { "IsEnabledValue", isEnabledValue },
                { buidColumn, buidValue }
            });

            // If one or more rows were affected, the update was successful
            return rowsAffected > 0;
        }

        public bool DoesValueExistCompositeKeyChecks(string tableName, string whereClause, Dictionary<string, object> parameters)
        {
            // Build the SELECT query to check if the value exists with the given condition
            string query = $"SELECT COUNT(*) FROM {tableName} WHERE {whereClause};";

            return ExecuteScalar<int>(query, parameters) > 0;
        }



        public bool DoesValueExistWithCondition(string tableName, string columnA, object valueA, string columnB, object valueB)
        {
            // Build the SELECT query to check if the value exists with the given condition
            string query = $"SELECT COUNT(*) FROM {tableName} WHERE {columnA} = @{columnA} AND {columnB} = @{columnB};";

            return ExecuteQueryWithCondition(query, columnA, valueA, columnB, valueB) > 0;
        }

        public DateTime? GetLatestDateForBUID(string tableName, string columnDate, string columnBUID, object bUIDValue)
        {
            // Build the SELECT query to get the latest date for the given BUID
            string query = $"SELECT MAX([{columnDate}]) FROM {tableName} WHERE [{columnBUID}] = @BUIDValue;";

            // Create parameters for the query
            var parameters = new Dictionary<string, object>
            {
                { "@BUIDValue", bUIDValue }
            };

            // Execute the scalar query using the ExecuteScalar<T> method
            object result = ExecuteScalar<object>(query, parameters);

            // Check if the result is not null or DBNull.Value, and convert it to DateTime
            if (result != null && result != DBNull.Value)
            {
                return Convert.ToDateTime(result);
            }

            // Return null if no date is found
            return null;
        }

        private int ExecuteQuery(string query, string columnA, object valueA)
        {
            // Create parameters
            var parameters = new Dictionary<string, object>
            {
                { columnA, valueA }
            };

            // Execute the query and get the result
            return ExecuteScalar<int>(query, parameters);
        }

        private int ExecuteQueryWithCondition(string query, string columnA, object valueA, string columnB, object valueB)
        {
            // Create parameters
            var parameters = new Dictionary<string, object>
            {
                { columnA, valueA },
                { columnB, valueB }
            };

            // Execute the query and get the result
            return ExecuteScalar<int>(query, parameters);
        }

        public Dictionary<string, string> GetGUIDAndCreatedDateDictionary(string buidDictTable, string buidColumn, object buidValue, string guidColumn, object guidValue, string detectedCreationDateColumn)
        {
            // Build the SELECT query to check if the value exists with the given condition
            string query = $"SELECT {guidColumn}, {detectedCreationDateColumn} FROM {buidDictTable} WHERE {buidColumn} = @{buidColumn} AND {guidColumn} = @{guidColumn};";

            // Create parameters
            var parameters = new Dictionary<string, object>
            {
                { buidColumn, buidValue },
                { guidColumn, guidValue }
            };

            // Execute the query and get the result
            Dictionary<string, string> resultDictionary = ExecuteScalarDictionary(query, parameters);

            return resultDictionary;
        }

        public Dictionary<string, string> GetGUIDAndLastUpdatedByDictionary(string guidColumn, string lastUpdatedByColumn, string progressOvertimeTable, string buidColumn, object buidValue)
        {
            // Build the SELECT query to check if the value exists with the given condition
            string query = $"SELECT {guidColumn}, {lastUpdatedByColumn} FROM {progressOvertimeTable} WHERE {buidColumn} = @{buidColumn};";

            // Create parameters
            var parameters = new Dictionary<string, object>
            {
                { buidColumn, buidValue }
            };

            // Execute the query and get the result
            Dictionary<string, string> resultDictionary = new Dictionary<string, string>();
            try
            {
                // Execute the query and get the result
                resultDictionary = ExecuteScalarDictionary(query, parameters);

            }
            catch
            {
                Console.WriteLine("Failed to Find GUID and LastUpdatedByDictonary");
            }
            return resultDictionary;

        }

        public Dictionary<string, string> GetGUIDAndLastUpdatedDateDictionary(string guidColumn, string lastUpdatedDateColumn, string progressOvertimeTable, string buidColumn, object buidValue)
        {
            // Build the SELECT query to check if the value exists with the given condition
            string query = $"SELECT {guidColumn}, {lastUpdatedDateColumn} FROM {progressOvertimeTable} WHERE {buidColumn} = @{buidColumn};";


            Dictionary<string, string> resultDictionary = new Dictionary<string, string>();
            // Create parameters
            var parameters = new Dictionary<string, object>
            {
                { buidColumn, buidValue }
            };
            try
            {
                // Execute the query and get the result
                resultDictionary = ExecuteScalarDictionary(query, parameters);
            }
            catch
            {
                Console.WriteLine("Failed to Find GUID and LastUpdatedDateDictionary");
            }

            return resultDictionary;
        }

        /// <summary>
        /// Executes a scalar query and returns the result of the specified type.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="query"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        private T ExecuteScalar<T>(string query, Dictionary<string, object> parameters = null)
        {
            // Create a new SqlConnection using the DatabaseConnection instance.
            return _policyWrap.Execute(() =>
            {
                using (SqlConnection connection = _databaseConnection.CreateConnection())
                {
                    using (SqlCommand command = _commandBuilder.CreateCommand(connection, query))
                    {
                        _commandBuilder.AddParameters(command, parameters);

                        connection.Open();

                        // Execute the scalar query and get the result.
                        object result = command.ExecuteScalar();

                        // Handle null values and convert to the desired type.
                        // If the result is DBNull.Value or null, return the default value for type T.
                        return (result == DBNull.Value || result == null) ? default(T) : (T)result;
                    }
                }
            });
        }

        private Dictionary<string, string> ExecuteScalarDictionary(string query, Dictionary<string, object> parameters = null)
        {
            // Create a new SqlConnection using the DatabaseConnection instance.
            using (SqlConnection connection = _databaseConnection.CreateConnection())
            {
                // Create a SqlCommand using the SqlCommandBuilder and the provided query.
                using (SqlCommand command = _commandBuilder.CreateCommand(connection, query))
                {
                    _commandBuilder.AddParameters(command, parameters);

                    connection.Open();
                    try
                    {
                        // Execute the query and get the SqlDataReader
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            // Initialize an empty dictionary to store results
                            Dictionary<string, string> resultDictionary = new Dictionary<string, string>();

                            // Loop through each row in the SqlDataReader
                            while (reader.Read())
                            {
                                // Assume that the first column is the key and second column is the value
                                string key = reader[0]?.ToString();
                                string value = reader[1]?.ToString();

                                // Add the key-value pair to the dictionary
                                // This assumes that the keys are unique
                                if (!resultDictionary.ContainsKey(key))
                                {
                                    resultDictionary.Add(key, value);
                                }
                                else
                                {
                                    // Handle duplicate keys how you see fit, e.g., throw an exception or ignore
                                }
                            }

                            return resultDictionary;
                        }
                       
                    }
                    catch
                    {
                        return new Dictionary<string, string>();
                    }
                }
            }
        }
    }
}
