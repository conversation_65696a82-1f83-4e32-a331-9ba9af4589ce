using System;
using System.IO;
using System.Reflection;

namespace BecaRevitUtilities.AzureSQL.CommonControllers
{
    /// <summary>
    /// Utility class to help set up the recommended configuration directory structure
    /// and manage configuration files across different environments
    /// </summary>
    public static class ConfigurationSetupUtility
    {
        /// <summary>
        /// Sets up the recommended development configuration structure
        /// Creates Config/Secure directory and template files
        /// </summary>
        /// <param name="projectRootPath">Path to the project root. If null, attempts to find automatically.</param>
        /// <returns>True if setup was successful</returns>
        public static bool SetupDevelopmentStructure(string projectRootPath = null)
        {
            try
            {
                string projectRoot = projectRootPath ?? FindProjectRoot();
                if (string.IsNullOrEmpty(projectRoot))
                {
                    Console.WriteLine("❌ Could not find project root directory");
                    return false;
                }

                // Create Config/Secure directory
                string secureConfigDir = Path.Combine(projectRoot, "Config", "Secure");
                Directory.CreateDirectory(secureConfigDir);
                Console.WriteLine($"✅ Created secure config directory: {secureConfigDir}");

                // Create Config/Templates directory
                string templatesDir = Path.Combine(projectRoot, "Config", "Templates");
                Directory.CreateDirectory(templatesDir);
                Console.WriteLine($"✅ Created templates directory: {templatesDir}");

                // Create template files
                CreateTemplateFiles(templatesDir);

                // Create .gitignore entry (if .gitignore exists)
                UpdateGitIgnore(projectRoot);

                // Create README for the Config directory
                CreateConfigReadme(Path.Combine(projectRoot, "Config"));

                Console.WriteLine("✅ Development configuration structure setup complete!");
                Console.WriteLine($"📁 Secure configs: {secureConfigDir}");
                Console.WriteLine($"📁 Templates: {templatesDir}");
                Console.WriteLine("⚠️  Remember to add actual connection details to the secure config files");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Failed to setup development structure: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Sets up configuration for the current deployment location (next to .dll)
        /// This is the recommended approach for production deployments
        /// </summary>
        /// <param name="configFileName">Name of the config file to create</param>
        /// <param name="server">Database server</param>
        /// <param name="database">Database name</param>
        /// <param name="username">Database username</param>
        /// <param name="password">Database password</param>
        /// <returns>True if setup was successful</returns>
        public static bool SetupDeploymentConfiguration(string configFileName, string server, string database, string username, string password)
        {
            try
            {
                // This will create the config file next to the executing assembly
                SecureConfigManager.CreateConfiguration(server, database, username, password, configFileName);

                string assemblyLocation = Assembly.GetExecutingAssembly().Location;
                string assemblyDir = Path.GetDirectoryName(assemblyLocation);
                string configPath = Path.Combine(assemblyDir, configFileName);

                Console.WriteLine($"✅ Deployment configuration created: {configPath}");
                Console.WriteLine("🔒 Configuration is encrypted and ready for use");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Failed to setup deployment configuration: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Sets up user-specific configuration in the user profile directory
        /// </summary>
        /// <param name="configFileName">Name of the config file to create</param>
        /// <param name="server">Database server</param>
        /// <param name="database">Database name</param>
        /// <param name="username">Database username</param>
        /// <param name="password">Database password</param>
        /// <returns>True if setup was successful</returns>
        public static bool SetupUserConfiguration(string configFileName, string server, string database, string username, string password)
        {
            try
            {
                string userProfilePath = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile);
                string userConfigDir = Path.Combine(userProfilePath, ".beca", "revit");
                Directory.CreateDirectory(userConfigDir);

                string userConfigPath = Path.Combine(userConfigDir, configFileName);

                // Create the configuration using SecureConfigManager
                SecureConfigManager.CreateConfiguration(server, database, username, password, configFileName);

                // Move the file from assembly location to user profile location
                string assemblyLocation = Assembly.GetExecutingAssembly().Location;
                string assemblyDir = Path.GetDirectoryName(assemblyLocation);
                string tempConfigPath = Path.Combine(assemblyDir, configFileName);

                if (File.Exists(tempConfigPath))
                {
                    File.Move(tempConfigPath, userConfigPath);
                }

                Console.WriteLine($"✅ User configuration created: {userConfigPath}");
                Console.WriteLine("👤 Configuration is stored in your user profile directory");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Failed to setup user configuration: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Shows the current configuration file locations and their status
        /// </summary>
        /// <param name="configFileName">Name of the config file to check</param>
        public static void ShowConfigurationStatus(string configFileName)
        {
            Console.WriteLine($"🔍 Configuration Status for: {configFileName}");
            Console.WriteLine(new string('=', 50));

            // Check assembly location (deployment)
            string assemblyLocation = Assembly.GetExecutingAssembly().Location;
            string assemblyDir = Path.GetDirectoryName(assemblyLocation);
            string assemblyConfigPath = Path.Combine(assemblyDir, configFileName);
            
            Console.WriteLine($"📦 Deployment Location: {assemblyConfigPath}");
            Console.WriteLine($"   Status: {(File.Exists(assemblyConfigPath) ? "✅ EXISTS" : "❌ NOT FOUND")}");

            // Check project directory
            string projectRoot = FindProjectRoot();
            if (!string.IsNullOrEmpty(projectRoot))
            {
                string projectConfigPath = Path.Combine(projectRoot, "Config", "Secure", configFileName);
                Console.WriteLine($"🔧 Development Location: {projectConfigPath}");
                Console.WriteLine($"   Status: {(File.Exists(projectConfigPath) ? "✅ EXISTS" : "❌ NOT FOUND")}");
            }

            // Check user profile
            string userProfilePath = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile);
            string userConfigPath = Path.Combine(userProfilePath, ".beca", "revit", configFileName);
            Console.WriteLine($"👤 User Profile Location: {userConfigPath}");
            Console.WriteLine($"   Status: {(File.Exists(userConfigPath) ? "✅ EXISTS" : "❌ NOT FOUND")}");

            // Test which one would be used
            try
            {
                string actualPath = SecureConfigManager.ValidateConfiguration(configFileName, null) ? "✅ WORKING" : "❌ NOT WORKING";
                Console.WriteLine($"🎯 Current Active Config: {actualPath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"🎯 Current Active Config: ❌ ERROR - {ex.Message}");
            }
        }

        /// <summary>
        /// Attempts to find the project root directory
        /// </summary>
        private static string FindProjectRoot()
        {
            try
            {
                string assemblyLocation = Assembly.GetExecutingAssembly().Location;
                string currentDir = Path.GetDirectoryName(assemblyLocation);

                while (currentDir != null && Path.GetPathRoot(currentDir) != currentDir)
                {
                    if (Directory.GetFiles(currentDir, "*.sln").Length > 0 ||
                        Directory.Exists(Path.Combine(currentDir, "COMMON")) ||
                        Directory.Exists(Path.Combine(currentDir, "MEP.PipeInsulation")))
                    {
                        return currentDir;
                    }
                    currentDir = Directory.GetParent(currentDir)?.FullName;
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Creates template configuration files
        /// </summary>
        private static void CreateTemplateFiles(string templatesDir)
        {
            // Create PipeInsulation template
            string pipeInsulationTemplate = Path.Combine(templatesDir, "PipeInsulation.config.template");
            File.WriteAllText(pipeInsulationTemplate, 
                "# PipeInsulation Configuration Template\n" +
                "# Copy this file to Config/Secure/PipeInsulation.config\n" +
                "# Replace the placeholder values with actual connection details\n" +
                "# The actual config file will be encrypted automatically\n\n" +
                "Server: your-server.database.windows.net\n" +
                "Database: YourDatabase\n" +
                "Username: your-username\n" +
                "Password: your-password\n");

            // Create SqlController template
            string sqlControllerTemplate = Path.Combine(templatesDir, "BecaAzureSQL_DevaBot.config.template");
            File.WriteAllText(sqlControllerTemplate,
                "# SqlController Configuration Template\n" +
                "# Copy this file to Config/Secure/BecaAzureSQL_DevaBot.config\n" +
                "# Replace the placeholder values with actual connection details\n" +
                "# The actual config file will be encrypted automatically\n\n" +
                "Server: devabotdbserver.database.windows.net\n" +
                "Database: DevaBot\n" +
                "Username: devabotadmin\n" +
                "Password: your-secure-password\n");

            Console.WriteLine("✅ Created template files");
        }

        /// <summary>
        /// Updates .gitignore to exclude secure configuration files
        /// </summary>
        private static void UpdateGitIgnore(string projectRoot)
        {
            try
            {
                string gitIgnorePath = Path.Combine(projectRoot, ".gitignore");
                string gitIgnoreEntry = "\n# Secure configuration files\nConfig/Secure/\n*.config\n!*.config.template\n.beca/\n";

                if (File.Exists(gitIgnorePath))
                {
                    string content = File.ReadAllText(gitIgnorePath);
                    if (!content.Contains("Config/Secure/"))
                    {
                        File.AppendAllText(gitIgnorePath, gitIgnoreEntry);
                        Console.WriteLine("✅ Updated .gitignore with secure config exclusions");
                    }
                }
                else
                {
                    File.WriteAllText(gitIgnorePath, gitIgnoreEntry);
                    Console.WriteLine("✅ Created .gitignore with secure config exclusions");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️  Could not update .gitignore: {ex.Message}");
            }
        }

        /// <summary>
        /// Creates a README file for the Config directory
        /// </summary>
        private static void CreateConfigReadme(string configDir)
        {
            string readmePath = Path.Combine(configDir, "README.md");
            string readmeContent = @"# Configuration Directory

## Structure
- `Secure/` - Actual encrypted configuration files (gitignored)
- `Templates/` - Template files for setting up configurations

## Setup Instructions
1. Copy template files from `Templates/` to `Secure/`
2. Remove `.template` extension
3. Update with actual connection details
4. The system will automatically encrypt the configurations

## Security Notes
- The `Secure/` directory is excluded from version control
- Configuration files are automatically encrypted
- Never commit actual connection strings to source control
";
            File.WriteAllText(readmePath, readmeContent);
            Console.WriteLine("✅ Created Config/README.md");
        }
    }
}
