﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<UseWindowsForms>true</UseWindowsForms>
		<UseWPF>true</UseWPF>
		<Configurations>Debug R20;Debug R21;Debug R22;Debug R23;Debug R24;Debug R25;Release R20;Release R21;Release R22;Release R23;Release R24;Release R25;Debug R26;Release R26</Configurations>
	</PropertyGroup>
	<ItemGroup>
	  <None Remove="UI\Views\BecaLogoBlack.png" />
	</ItemGroup>


	<ItemGroup>
		<Folder Include="UI\ModelessRevitForm\" />
		<Folder Include="UI\Views\Images\" />
	</ItemGroup>


	<ItemGroup>
	  <PackageReference Include="CsvHelper" Version="1.0.0" />
	  <PackageReference Include="Microsoft.Office.Interop.Excel" Version="15.0.4795.1001" />
	</ItemGroup>


	<ItemGroup>
	  <ProjectReference Include="..\..\COMMON\BecaActivityLogger\BecaActivityLogger.csproj" />
	  <ProjectReference Include="..\..\COMMON\BecaCommand\BecaCommand.csproj" />
	  <ProjectReference Include="..\..\COMMON\BecaRevitUtilities\BecaRevitUtilities.csproj" />
	  <ProjectReference Include="..\..\COMMON\BecaTransactionsNames\BecaTransactionsNamesManager.csproj" />
	  <ProjectReference Include="..\..\COMMON\BecaTrekaHandler\BecaTrekaHandler.csproj" />
	  <ProjectReference Include="..\..\COMMON\Common.ExcelInterop\Common.ExcelInterop.csproj" />
	  <ProjectReference Include="..\..\COMMON\Common.Extenstions\Common.Extenstions.csproj" />
	  <ProjectReference Include="..\..\COMMON\Common.UI.WPF\Common.UI.WPF.csproj" />
	  <ProjectReference Include="..\..\COMMON\Common.UI\Common.UI.csproj" />
	  <ProjectReference Include="..\..\COMMON\Common.Utilities\Common.Utilities.csproj" />
	  <ProjectReference Include="..\..\GEN\GEN.LinkedFileMapper\GEN.LinkedFileMapper.csproj" />
	</ItemGroup>


	<ItemGroup>
	  <Resource Include="UI\Views\BecaLogoBlack.png" />
	</ItemGroup>


	<ItemGroup>
	  <Compile Update="Properties\Resources.Designer.cs">
	    <DesignTime>True</DesignTime>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>Resources.resx</DependentUpon>
	  </Compile>
	  <Compile Update="UI\Views\PI_MainWindow.xaml.cs">
	    <SubType>Code</SubType>
	  </Compile>
	</ItemGroup>


	<ItemGroup>
	  <EmbeddedResource Update="Properties\Resources.resx">
	    <Generator>ResXFileCodeGenerator</Generator>
	    <LastGenOutput>Resources.Designer.cs</LastGenOutput>
	  </EmbeddedResource>
	</ItemGroup>


	<ItemGroup>
	  <Page Update="UI\Views\PI_MainWindow.xaml">
	    <SubType>Designer</SubType>
	  </Page>
	</ItemGroup>

</Project>