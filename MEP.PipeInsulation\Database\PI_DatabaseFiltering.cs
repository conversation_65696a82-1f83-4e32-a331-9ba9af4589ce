﻿using Autodesk.Revit.DB;
using MEP.PipeInsulation.CoreLogic;
using MEP.PipeInsulation.Models;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PipeInsulation.Database
{
    class PI_DatabaseFiltering
    {
        public static List<string> MaterialOptions = new List<string>
        { 
            "Worst-case", 
            "Elastometric", 
            "Polyethylene", 
            "Phenolic", 
            "Fibreglass" 
        };

        public static List<string> DataSourceOptions = new List<string>
        {
            PI_SourceNames.NZAU,
            PI_SourceNames.SG,
            PI_SourceNames.Custom
        };

        public static List<string> CommercialOptions = new List<string>
        {
            PI_CommercialNames.Commercial,
            PI_CommercialNames.NonCommercial
        };

        public static List<string> DesignLevelOptions = new List<string>
        {
            PI_DesignLevelNames.Base,
            PI_DesignLevelNames.Enhanced
        };

        public static double FilterCalculatedThickness(DataTable dataTable, PI_Element element, string systemTypeName, string selectedDataSource, string designLevel, string commercial, string material)
        {
            IEnumerable<DataRow> filteredRows = new List<DataRow>();

            if (material == "Worst-case")
            {
                filteredRows = from DataRow row in dataTable.Rows
                                   where row[PI_DatabaseTableColumnNames.Country].ToString() == selectedDataSource
                                      && row[PI_DatabaseTableColumnNames.System].ToString() == systemTypeName
                                      && Convert.ToInt32(row[PI_DatabaseTableColumnNames.MinDiameter]) <= element.Diameter
                                      && Convert.ToInt32(row[PI_DatabaseTableColumnNames.MaxDiameter]) >= element.Diameter
                                      && row[PI_DatabaseTableColumnNames.Location].ToString() == (element.IsInterior ? "Interior" : "Exterior")
                                      && (row[PI_DatabaseTableColumnNames.DesignLevel].ToString() == designLevel || row[PI_DatabaseTableColumnNames.DesignLevel].ToString() == PI_DesignLevelNames.NotApplicable)
                                      && (row[PI_DatabaseTableColumnNames.Commercial].ToString() == commercial || row[PI_DatabaseTableColumnNames.Commercial].ToString() == PI_DesignLevelNames.NotApplicable)
                                   select row;

                return GetTheWorstCaseValue(filteredRows);
            }
            else
            {
                filteredRows = from DataRow row in dataTable.Rows
                               where row[PI_DatabaseTableColumnNames.Country].ToString() == selectedDataSource
                                  && row[PI_DatabaseTableColumnNames.System].ToString() == systemTypeName
                                  && Convert.ToInt32(row[PI_DatabaseTableColumnNames.MinDiameter]) <= element.Diameter
                                  && Convert.ToInt32(row[PI_DatabaseTableColumnNames.MaxDiameter]) >= element.Diameter
                                  && row[PI_DatabaseTableColumnNames.Location].ToString() == (element.IsInterior ? "Interior" : "Exterior")
                                  && (row[PI_DatabaseTableColumnNames.DesignLevel].ToString() == designLevel || row[PI_DatabaseTableColumnNames.DesignLevel].ToString() == PI_DesignLevelNames.NotApplicable)
                                  && (row[PI_DatabaseTableColumnNames.Commercial].ToString() == commercial || row[PI_DatabaseTableColumnNames.Commercial].ToString() == PI_DesignLevelNames.NotApplicable)
                                  && row[PI_DatabaseTableColumnNames.Material].ToString() == material
                               select row;

                return filteredRows.Any() ? Convert.ToDouble(filteredRows.First()[PI_DatabaseTableColumnNames.Value]) : 0;
            }
        }

        private static double GetTheWorstCaseValue(IEnumerable<DataRow> filteredRows)
        {
            if (filteredRows == null || !filteredRows.Any())
            {
                return 0; 
            }

            double maxValue = double.MinValue; // Initialize with the smallest possible value

            foreach (var row in filteredRows)
            {
                var value = row[PI_DatabaseTableColumnNames.Value];

                if (value is DBNull) continue;

                // Compare and update maxValue
                double numericValue = Convert.ToDouble(value);
                maxValue = Math.Max(maxValue, numericValue);
            }

            return maxValue == double.MinValue ? 0 : maxValue; // Return 0 if no valid values were found
        }
    }
}
