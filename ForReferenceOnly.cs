using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace SecretProtector
{
    class Program
    {
        static void Main()
        {
            // Step 1: Generate Secret Dynamically via Keygen Algorithm
            string secret = KeygenSecret();
            Console.WriteLine($"Generated Secret: {secret}");

            // Step 2: Generate Runtime AES Key and IV
            using Aes aes = Aes.Create();
            aes.KeySize = 128;
            aes.GenerateKey();
            aes.GenerateIV();

            // Step 3: Encrypt Secret in Memory
            byte[] encryptedSecret = EncryptString(secret, aes.Key, aes.IV);
            Console.WriteLine($"Encrypted Secret (Base64): {Convert.ToBase64String(encryptedSecret)}");

            // Step 4: Decrypt when needed
            string decryptedSecret = DecryptString(encryptedSecret, aes.Key, aes.IV);
            Console.WriteLine($"Decrypted Secret: {decryptedSecret}");

            // Step 5 (Optional): Wipe sensitive data from memory
            Array.Clear(encryptedSecret, 0, encryptedSecret.Length);
            secret = null;
            decryptedSecret = null;

            // Hold Console
            Console.ReadLine();
        }

        // --- Keygen Algorithm ---
        static string KeygenSecret()
        {
            // Example: generate 'hideMe' without hardcoding it.
            int[] seeds = { 500, 1000, 1500, 2000, 2500, 3000 };
            StringBuilder sb = new StringBuilder();

            foreach (var seed in seeds)
            {
                int charCode = (seed / 25) ^ 42;
                sb.Append((char)charCode);
            }

            return sb.ToString();  // returns 'hideMe'
        }

        // --- AES Encryption ---
        static byte[] EncryptString(string plainText, byte[] Key, byte[] IV)
        {
            using MemoryStream msEncrypt = new MemoryStream();
            using Aes aesAlg = Aes.Create();
            aesAlg.Key = Key;
            aesAlg.IV = IV;

            ICryptoTransform encryptor = aesAlg.CreateEncryptor(aesAlg.Key, aesAlg.IV);
            using CryptoStream csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write);
            using (StreamWriter swEncrypt = new StreamWriter(csEncrypt))
            {
                swEncrypt.Write(plainText);
            }

            return msEncrypt.ToArray();
        }

        // --- AES Decryption ---
        static string DecryptString(byte[] cipherText, byte[] Key, byte[] IV)
        {
            using Aes aesAlg = Aes.Create();
            aesAlg.Key = Key;
            aesAlg.IV = IV;

            ICryptoTransform decryptor = aesAlg.CreateDecryptor(aesAlg.Key, aesAlg.IV);
            using MemoryStream msDecrypt = new MemoryStream(cipherText);
            using CryptoStream csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
            using StreamReader srDecrypt = new StreamReader(csDecrypt);
            {
                return srDecrypt.ReadToEnd();
            }
        }
    }
}
