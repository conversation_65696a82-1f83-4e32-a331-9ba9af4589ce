﻿<Page
    x:Class="MEP.PipeInsulation.UI.Views.DataSources.PI_DataSourceViewNonCard"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.PipeInsulation.UI.Views.DataSources"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="PI_DataSourceViewNonCard"
    d:DesignHeight="450"
    d:DesignWidth="800"
    Background="White"
    mc:Ignorable="d">

    <!--  Window specific resources  -->
    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Page.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  Header  -->
        <TextBlock
            Grid.Row="0"
            Grid.ColumnSpan="2"
            Margin="10"
            Style="{StaticResource MaterialDesignHeadline6TextBlock}"
            Text="{Binding SelectedDataSource}" />

        <GroupBox Grid.Row="1" Margin="10">
            <DataGrid
                x:Name="dgv_Lookup"
                Margin="10"
                AlternatingRowBackground="WhiteSmoke"
                AutoGenerateColumns="True"
                CanUserAddRows="False"
                EnableRowVirtualization="True" ColumnHeaderHeight="50"
                GridLinesVisibility="All"
                ItemsSource="{Binding ViewTable.DefaultView}"
                ScrollViewer.HorizontalScrollBarVisibility="Auto"
                ScrollViewer.VerticalScrollBarVisibility="Auto"
                Visibility="Visible">
                <!--<DataGrid.Columns>
                    <DataGridTextColumn
                        Binding="{Binding System}"
                        Header="Pipe System&#x0a;"
                        IsReadOnly="True" />
                    <DataGridTextColumn
                        Binding="{Binding CNC}"
                        Header="Design&#x0a;Level"
                        IsReadOnly="True" />
                    <DataGridTextColumn Binding="{Binding MinDia}" IsReadOnly="True">
                        <DataGridTextColumn.Header>
                            <TextBlock TextAlignment="Left">
                                Minimum<LineBreak />
                                Diameter</TextBlock>
                        </DataGridTextColumn.Header>
                    </DataGridTextColumn>
                    <DataGridTextColumn Binding="{Binding MaxDia}" IsReadOnly="True">
                        <DataGridTextColumn.Header>
                            <TextBlock TextAlignment="Left">
                                Maximum<LineBreak />
                                Diameter</TextBlock>
                        </DataGridTextColumn.Header>
                    </DataGridTextColumn>
                    <DataGridTextColumn Binding="{Binding InteriorElastometric}" IsReadOnly="True">
                        <DataGridTextColumn.Header>
                            <TextBlock TextAlignment="Left">
                                Interior<LineBreak />
                                Elastometric</TextBlock>
                        </DataGridTextColumn.Header>
                    </DataGridTextColumn>
                    <DataGridTextColumn Binding="{Binding InteriorPolyethylene}" IsReadOnly="True">
                        <DataGridTextColumn.Header>
                            <TextBlock TextAlignment="Left">
                                Interior<LineBreak />
                                Polyethylene</TextBlock>
                        </DataGridTextColumn.Header>
                    </DataGridTextColumn>
                    <DataGridTextColumn Binding="{Binding InteriorPhenolic}" IsReadOnly="True">
                        <DataGridTextColumn.Header>
                            <TextBlock TextAlignment="Left">
                                Interior<LineBreak />
                                Phenolic</TextBlock>
                        </DataGridTextColumn.Header>
                    </DataGridTextColumn>
                    <DataGridTextColumn Binding="{Binding InteriorFibreglass}" IsReadOnly="True">
                        <DataGridTextColumn.Header>
                            <TextBlock TextAlignment="Left">
                                Interior<LineBreak />
                                Fibreglass</TextBlock>
                        </DataGridTextColumn.Header>
                    </DataGridTextColumn>
                    <DataGridTextColumn Binding="{Binding ExteriorElastometric}" IsReadOnly="True">
                        <DataGridTextColumn.Header>
                            <TextBlock TextAlignment="Left">
                                Exterior<LineBreak />
                                Elastometric</TextBlock>
                        </DataGridTextColumn.Header>
                    </DataGridTextColumn>
                    <DataGridTextColumn Binding="{Binding ExteriorPolyethylene}" IsReadOnly="True">
                        <DataGridTextColumn.Header>
                            <TextBlock TextAlignment="Left">
                                Exterior<LineBreak />
                                Polyethylene</TextBlock>
                        </DataGridTextColumn.Header>
                    </DataGridTextColumn>
                    <DataGridTextColumn Binding="{Binding ExteriorPhenolic}" IsReadOnly="True">
                        <DataGridTextColumn.Header>
                            <TextBlock TextAlignment="Left">
                                Exterior<LineBreak />
                                Phenolic</TextBlock>
                        </DataGridTextColumn.Header>
                    </DataGridTextColumn>
                    <DataGridTextColumn Binding="{Binding ExteriorFibreglass}" IsReadOnly="True">
                        <DataGridTextColumn.Header>
                            <TextBlock TextAlignment="Left">
                                Exterior<LineBreak />
                                Fibreglass</TextBlock>
                        </DataGridTextColumn.Header>
                    </DataGridTextColumn>
                </DataGrid.Columns>-->
            </DataGrid>
        </GroupBox>

        <StackPanel
            Grid.Row="2"
            HorizontalAlignment="Center"
            Orientation="Horizontal">
            <Button
                x:Name="btn_back"
                Width="120"
                Margin="0,10,50,10"
                HorizontalAlignment="Center"
                Background="#12A8B2"
                BorderBrush="#12A8B2"
                Click="btn_back_Click"
                Content="Back"
                Foreground="White" />
            <Button
                x:Name="btn_SaveCustom"
                Width="120"
                Background="#FFCE00"
                BorderBrush="#FFCE00"
                Click="btn_SaveCustom_Click"
                Content="Save Custom" />
        </StackPanel>

    </Grid>
</Page>
