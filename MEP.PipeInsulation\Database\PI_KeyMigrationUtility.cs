using System;
using System.IO;

namespace MEP.PipeInsulation.Database
{
    /// <summary>
    /// Utility to help migrate from old hardcoded encryption key to new algorithmic key
    /// This ensures backward compatibility while providing enhanced security for new installations
    /// </summary>
    public static class PI_KeyMigrationUtility
    {
        /// <summary>
        /// Migrates existing configuration from old hardcoded key to new algorithmic key
        /// </summary>
        /// <returns>True if migration was successful or not needed</returns>
        public static bool MigrateToNewKey()
        {
            try
            {
                Console.WriteLine("🔄 Starting key migration process...");

                // Check if config file exists
                string configPath = GetConfigFilePath();
                if (!File.Exists(configPath))
                {
                    Console.WriteLine("ℹ️  No existing config file found. New installations will use algorithmic key.");
                    return true;
                }

                // Try to decrypt with old key
                string encryptedContent = File.ReadAllText(configPath);
                string connectionString = null;

                try
                {
                    connectionString = DecryptWithOldKey(encryptedContent);
                    Console.WriteLine("✅ Successfully decrypted existing config with legacy key");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ Could not decrypt with legacy key: {ex.Message}");
                    
                    // Try with new key (maybe already migrated)
                    try
                    {
                        connectionString = DecryptWithNewKey(encryptedContent);
                        Console.WriteLine("✅ Config is already using new algorithmic key");
                        return true;
                    }
                    catch
                    {
                        Console.WriteLine("❌ Could not decrypt with either key. Manual intervention required.");
                        return false;
                    }
                }

                // Create backup
                string backupPath = configPath + ".backup." + DateTime.Now.ToString("yyyyMMdd_HHmmss");
                File.Copy(configPath, backupPath);
                Console.WriteLine($"📁 Created backup: {backupPath}");

                // Re-encrypt with new key
                string newEncryptedContent = EncryptWithNewKey(connectionString);
                File.WriteAllText(configPath, newEncryptedContent);
                Console.WriteLine("🔑 Re-encrypted config file with new algorithmic key");

                // Verify the migration
                try
                {
                    string verifyConnectionString = PI_SecureConfigManager.GetConnectionString();
                    if (!string.IsNullOrEmpty(verifyConnectionString))
                    {
                        Console.WriteLine("✅ Migration verification successful");
                        return true;
                    }
                    else
                    {
                        throw new Exception("Verification returned empty connection string");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ Migration verification failed: {ex.Message}");
                    
                    // Restore backup
                    File.Copy(backupPath, configPath, true);
                    Console.WriteLine("🔄 Restored from backup due to verification failure");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Migration failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Forces the use of the new algorithmic key (for new installations)
        /// </summary>
        /// <param name="connectionString">Connection string to encrypt with new key</param>
        /// <returns>True if successful</returns>
        public static bool CreateWithNewKey(string connectionString)
        {
            try
            {
                string configPath = GetConfigFilePath();
                string encryptedContent = EncryptWithNewKey(connectionString);
                
                // Ensure directory exists
                Directory.CreateDirectory(Path.GetDirectoryName(configPath));
                
                File.WriteAllText(configPath, encryptedContent);
                Console.WriteLine("🔑 Created new config file with algorithmic key");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Failed to create config with new key: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Shows which key is currently being used
        /// </summary>
        public static void ShowCurrentKeyType()
        {
            try
            {
                string configPath = GetConfigFilePath();
                if (!File.Exists(configPath))
                {
                    Console.WriteLine("ℹ️  No config file exists. New installations will use algorithmic key.");
                    return;
                }

                string encryptedContent = File.ReadAllText(configPath);

                // Test with old key
                try
                {
                    DecryptWithOldKey(encryptedContent);
                    Console.WriteLine("🔑 Current config uses: LEGACY HARDCODED KEY");
                    Console.WriteLine("💡 Consider running MigrateToNewKey() for enhanced security");
                    return;
                }
                catch { }

                // Test with new key
                try
                {
                    DecryptWithNewKey(encryptedContent);
                    Console.WriteLine("🔑 Current config uses: NEW ALGORITHMIC KEY ✅");
                    return;
                }
                catch { }

                Console.WriteLine("❌ Could not decrypt config with either key type");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error checking key type: {ex.Message}");
            }
        }

        /// <summary>
        /// Compares the old and new keys (for debugging)
        /// </summary>
        public static void CompareKeys()
        {
            Console.WriteLine("🔍 Key Comparison:");
            Console.WriteLine(new string('=', 50));

            // Generate old key
            string oldKey = GetOldCompanyKey();
            string oldEncryptionKey = GetLegacyEncryptionKey(oldKey);
            Console.WriteLine($"🔑 Old hardcoded key: {oldKey}");
            Console.WriteLine($"🔒 Old encryption key (SHA256): {oldEncryptionKey.Substring(0, 20)}...");

            // Generate new key
            string newKey = GenerateNewCompanyKey();
            string newEncryptionKey = GetNewEncryptionKey(newKey);
            Console.WriteLine($"🔑 New algorithmic key: {newKey}");
            Console.WriteLine($"🔒 New encryption key (SHA256): {newEncryptionKey.Substring(0, 20)}...");

            Console.WriteLine($"🔄 Keys are different: {oldKey != newKey}");
        }

        // Helper methods that replicate the encryption logic for migration
        private static string GetConfigFilePath()
        {
            string assemblyLocation = System.Reflection.Assembly.GetExecutingAssembly().Location;
            string assemblyDirectory = Path.GetDirectoryName(assemblyLocation);
            return Path.Combine(assemblyDirectory, "PipeInsulation.config");
        }

        private static string DecryptWithOldKey(string cipherText)
        {
            string oldKey = GetOldCompanyKey();
            string encryptionKey = GetLegacyEncryptionKey(oldKey);
            return DecryptString(cipherText, encryptionKey);
        }

        private static string DecryptWithNewKey(string cipherText)
        {
            string newKey = GenerateNewCompanyKey();
            string encryptionKey = GetNewEncryptionKey(newKey);
            return DecryptString(cipherText, encryptionKey);
        }

        private static string EncryptWithNewKey(string plainText)
        {
            string newKey = GenerateNewCompanyKey();
            string encryptionKey = GetNewEncryptionKey(newKey);
            return EncryptString(plainText, encryptionKey);
        }

        private static string GetOldCompanyKey()
        {
            return "RKE1jyALPgtUHsh2UKFdcG1HoBOyCdA7";
        }

        private static string GenerateNewCompanyKey()
        {
            // Same algorithm as in PI_SecureConfigManager.GenerateCompanyKey()
            int[] seeds = { 2100, 2500, 2200, 1800, 2700, 2400, 2300, 2600, 2000, 1900, 
                           2800, 2150, 1950, 2350, 2050, 2450, 1850, 2750, 2250, 2550,
                           2650, 1750, 2850, 2950, 1650, 2175, 2375, 2575, 2775, 2975,
                           1575, 2125 };
            
            System.Text.StringBuilder sb = new System.Text.StringBuilder();
            
            for (int i = 0; i < seeds.Length; i++)
            {
                int seed = seeds[i];
                int transform1 = (seed / 25) ^ 42;
                int transform2 = (transform1 + i * 7) % 126;
                int transform3 = transform2 < 32 ? transform2 + 65 : transform2;
                
                if (transform3 > 126) transform3 = (transform3 % 95) + 32;
                if (transform3 < 32) transform3 += 32;
                
                sb.Append((char)transform3);
            }
            
            return sb.ToString();
        }

        private static string GetLegacyEncryptionKey(string companyKey)
        {
            using (var sha256 = System.Security.Cryptography.SHA256.Create())
            {
                byte[] hash = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(companyKey));
                return Convert.ToBase64String(hash);
            }
        }

        private static string GetNewEncryptionKey(string companyKey)
        {
            using (var sha256 = System.Security.Cryptography.SHA256.Create())
            {
                byte[] hash = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(companyKey));
                return Convert.ToBase64String(hash);
            }
        }

        // Simplified encryption/decryption methods (same as PI_SecureConfigManager)
        private static string EncryptString(string plainText, string key)
        {
            byte[] keyBytes = Convert.FromBase64String(key);

            using (var aes = System.Security.Cryptography.Aes.Create())
            {
                aes.Key = keyBytes;
                aes.GenerateIV();

                using (var encryptor = aes.CreateEncryptor())
                using (var msEncrypt = new System.IO.MemoryStream())
                {
                    msEncrypt.Write(aes.IV, 0, aes.IV.Length);

                    using (var csEncrypt = new System.Security.Cryptography.CryptoStream(msEncrypt, encryptor, System.Security.Cryptography.CryptoStreamMode.Write))
                    using (var swEncrypt = new System.IO.StreamWriter(csEncrypt))
                    {
                        swEncrypt.Write(plainText);
                    }

                    return Convert.ToBase64String(msEncrypt.ToArray());
                }
            }
        }

        private static string DecryptString(string cipherText, string key)
        {
            byte[] keyBytes = Convert.FromBase64String(key);
            byte[] cipherBytes = Convert.FromBase64String(cipherText);

            using (var aes = System.Security.Cryptography.Aes.Create())
            {
                aes.Key = keyBytes;

                byte[] iv = new byte[aes.BlockSize / 8];
                Array.Copy(cipherBytes, 0, iv, 0, iv.Length);
                aes.IV = iv;

                using (var decryptor = aes.CreateDecryptor())
                using (var msDecrypt = new System.IO.MemoryStream(cipherBytes, iv.Length, cipherBytes.Length - iv.Length))
                using (var csDecrypt = new System.Security.Cryptography.CryptoStream(msDecrypt, decryptor, System.Security.Cryptography.CryptoStreamMode.Read))
                using (var srDecrypt = new System.IO.StreamReader(csDecrypt))
                {
                    return srDecrypt.ReadToEnd();
                }
            }
        }
    }
}
