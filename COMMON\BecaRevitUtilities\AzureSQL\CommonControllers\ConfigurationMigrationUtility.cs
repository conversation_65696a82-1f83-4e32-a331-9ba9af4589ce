using System;
using System.IO;
using System.Configuration;

namespace BecaRevitUtilities.AzureSQL.CommonControllers
{
    /// <summary>
    /// Utility class to help migrate from project-specific secure configuration managers
    /// to the common SecureConfigManager
    /// </summary>
    public static class ConfigurationMigrationUtility
    {
        /// <summary>
        /// Migrates configuration from Pipe Insulation project to common configuration
        /// </summary>
        /// <param name="preserveOriginal">If true, keeps the original config file</param>
        /// <returns>True if migration was successful</returns>
        public static bool MigratePipeInsulationConfig(bool preserveOriginal = true)
        {
            try
            {
                // Try to get connection string from the old PI_SecureConfigManager approach
                string oldConfigPath = GetOldPipeInsulationConfigPath();
                
                if (File.Exists(oldConfigPath))
                {
                    // Read the old encrypted config and decrypt it using the old method
                    string encryptedContent = File.ReadAllText(oldConfigPath);
                    string connectionString = DecryptWithOldMethod(encryptedContent);
                    
                    // Create new configuration using the common SecureConfigManager
                    var provider = SecureConnectionProviderFactory.CreatePipeInsulationProvider();
                    provider.CreateEncryptedConfig(connectionString);
                    
                    // Optionally remove the old config file
                    if (!preserveOriginal)
                    {
                        File.Delete(oldConfigPath);
                    }
                    
                    return true;
                }
                
                // Try to get from app.config as fallback
                var appConfigConnectionString = ConfigurationManager.ConnectionStrings["PipeInsulationDB"]?.ConnectionString;
                if (!string.IsNullOrEmpty(appConfigConnectionString))
                {
                    var provider = SecureConnectionProviderFactory.CreatePipeInsulationProvider();
                    provider.CreateEncryptedConfig(appConfigConnectionString);
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                // Log the error or handle it appropriately
                Console.WriteLine($"Migration failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Gets the path to the old Pipe Insulation config file
        /// </summary>
        private static string GetOldPipeInsulationConfigPath()
        {
            string assemblyLocation = System.Reflection.Assembly.GetExecutingAssembly().Location;
            string assemblyDirectory = Path.GetDirectoryName(assemblyLocation);
            return Path.Combine(assemblyDirectory, "PipeInsulation.config");
        }

        /// <summary>
        /// Decrypts using the old method (with hardcoded key) for migration purposes
        /// This method replicates the old encryption logic to read existing config files
        /// </summary>
        private static string DecryptWithOldMethod(string cipherText)
        {
            // This uses the old hardcoded key for migration purposes only
            const string oldCompanyKey = "RKE1jyALPgtUHsh2UKFdcG1HoBOyCdA7";
            
            using (var sha256 = System.Security.Cryptography.SHA256.Create())
            {
                byte[] hash = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(oldCompanyKey));
                string oldEncryptionKey = Convert.ToBase64String(hash);
                
                return DecryptStringWithKey(cipherText, oldEncryptionKey);
            }
        }

        /// <summary>
        /// Decrypts a string using the specified key (for migration purposes)
        /// </summary>
        private static string DecryptStringWithKey(string cipherText, string key)
        {
            byte[] keyBytes = Convert.FromBase64String(key);
            byte[] cipherBytes = Convert.FromBase64String(cipherText);

            using (var aes = System.Security.Cryptography.Aes.Create())
            {
                aes.Key = keyBytes;

                // Extract IV from the beginning of the cipher text
                byte[] iv = new byte[aes.BlockSize / 8];
                Array.Copy(cipherBytes, 0, iv, 0, iv.Length);
                aes.IV = iv;

                using (var decryptor = aes.CreateDecryptor())
                using (var msDecrypt = new MemoryStream(cipherBytes, iv.Length, cipherBytes.Length - iv.Length))
                using (var csDecrypt = new System.Security.Cryptography.CryptoStream(msDecrypt, decryptor, System.Security.Cryptography.CryptoStreamMode.Read))
                using (var srDecrypt = new StreamReader(csDecrypt))
                {
                    return srDecrypt.ReadToEnd();
                }
            }
        }

        /// <summary>
        /// Validates that the migration was successful by testing the new configuration
        /// </summary>
        /// <returns>True if the new configuration is working</returns>
        public static bool ValidateMigration()
        {
            try
            {
                var provider = SecureConnectionProviderFactory.CreatePipeInsulationProvider();
                return provider.ValidateConfiguration();
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Creates a backup of the current configuration before migration
        /// </summary>
        /// <returns>Path to the backup file, or null if backup failed</returns>
        public static string CreateConfigBackup()
        {
            try
            {
                string oldConfigPath = GetOldPipeInsulationConfigPath();
                if (File.Exists(oldConfigPath))
                {
                    string backupPath = oldConfigPath + ".backup." + DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    File.Copy(oldConfigPath, backupPath);
                    return backupPath;
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Restores configuration from a backup file
        /// </summary>
        /// <param name="backupPath">Path to the backup file</param>
        /// <returns>True if restore was successful</returns>
        public static bool RestoreFromBackup(string backupPath)
        {
            try
            {
                if (File.Exists(backupPath))
                {
                    string originalPath = GetOldPipeInsulationConfigPath();
                    File.Copy(backupPath, originalPath, true);
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
    }
}
