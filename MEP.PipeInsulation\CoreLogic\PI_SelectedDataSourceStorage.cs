﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.ExtensibleStorage;
using MEP.PipeInsulation.CoreLogic.LogicEnhanced;
using MEP.PipeInsulation.Database;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PipeInsulation.CoreLogic
{
    public static class PI_SelectedDataSourceStorage
    {
        // Unique GUID for identifying your schema
        private static readonly Guid schemaGuid = new Guid("CE67474C-64F0-472C-B254-91E628193ABB");
        private static readonly string selectedDataSource = "SelectedDataSource";

        /// <summary>
        /// Creates or retrieves the schema for the SelectedDataSource data storage.
        /// </summary>
        /// <returns>The schema object.</returns>
        private static Schema GetOrCreateSchema(Document doc)
        {
            // Check if the schema already exists
            Schema existingSchema = Schema.Lookup(schemaGuid);
            if (existingSchema != null)
            {
                return existingSchema;
            }

            // Define the schema if it does not exist
            SchemaBuilder schemaBuilder = new SchemaBuilder(schemaGuid);
            schemaBuilder.SetSchemaName("PI_SelectedDataSource");
            schemaBuilder.SetDocumentation("Stores selected DataSource string in Project Information.");

            // Create a single field for storing the custom string
            schemaBuilder.AddSimpleField(selectedDataSource, typeof(string))
                         .SetDocumentation("A custom string value associated with the project.");

            // Finalize and return the schema
            Schema schema = schemaBuilder.Finish();

            // Set up the default entity on Project Information if no entity exists
            ProjectInfo projectInfo = doc.ProjectInformation;
            if (projectInfo == null)
            {
                throw new InvalidOperationException("Project Information element is unavailable.");
            }

            // Create an entity for the schema
            Entity defaultEntity = new Entity(schema);

            defaultEntity.Set(selectedDataSource, PI_DataSource.NZ_AU_Base.ToString()); 

            using (Transaction transaction = new Transaction(doc, "Initialize default selected DataSource"))
            {
                transaction.Start();
                projectInfo.SetEntity(defaultEntity);
                transaction.Commit();
            }

            return schema;
        }

        /// <summary>
        /// Saves custom data to the Project Information element.
        /// </summary>
        /// <param name="doc">The active Revit document.</param>
        /// <param name="value">The string to save.</param>
        public static void SaveSelectedDataSourceString(Document doc, string value)
        {
            // Get the Project Information element
            ProjectInfo projectInfo = doc.ProjectInformation;

            if (projectInfo == null)
            {
                throw new InvalidOperationException("Project Information element is unavailable.");
            }

            // Get or create the schema
            Schema schema = GetOrCreateSchema(doc);

            // Create an entity based on the schema
            Entity entity = new Entity(schema);
            entity.Set(selectedDataSource, value); // Store the custom string in the entity

            // Attach the entity to the Project Information element
            projectInfo.SetEntity(entity);
        }

        /// <summary>
        /// Retrieves the custom data from the Project Information element.
        /// </summary>
        /// <param name="doc">The active Revit document.</param>
        /// <returns>The stored custom string, or null if not found.</returns>
        public static string GetSelectedDataSourceString(Document doc)
        {
            // Get the Project Information element
            ProjectInfo projectInfo = doc.ProjectInformation;

            if (projectInfo == null)
            {
                throw new InvalidOperationException("Project Information element is unavailable.");
            }

            // Get the schema
            Schema schema = GetOrCreateSchema(doc);

            // Retrieve the stored entity from the element
            Entity entity = projectInfo.GetEntity(schema);

            // Return the stored string value if it exists
            return entity?.Get<string>(selectedDataSource);
        }
    }
}
