﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PipeInsulation.Database
{
    public static class PI_DatabaseTableColumnNames
    {
        public static string Country = "Country";
        public static string System = "System";
        public static string MinDiameter = "MinDiameter";
        public static string MaxDiameter = "MaxDiameter";
        public static string Location = "Location";
        public static string DesignLevel = "DesignLevel";
        public static string Commercial = "Commercial";
        public static string Material = "Material";
        public static string Value = "Value";

    }

    public static class PI_SourceNames
    {
        public static string NZAU = "NZ+AU";
        public static string SG = "SG";
        public static string Custom = "Custom";

    }

    public static class PI_DesignLevelNames
    {
        public static string NotApplicable = "Not Applicable";
        public static string Base = "Base"; 
        public static string Enhanced = "Enhanced";
    }

    public static class PI_CommercialNames
    {
        public static string NotApplicable = "Not Applicable";
        public static string Commercial = "Commercial";
        public static string NonCommercial = "Non-Commercial";
    }
}
