﻿using Common.UI.Forms;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MEP.PipeInsulation.UI.Forms
{
    public partial class FrmbecaMechPipeInsAll : BecaBaseForm
    {

        #region Fields


        #endregion

        #region Propertises


        #endregion

        #region Constructors


        public FrmbecaMechPipeInsAll()
        {
            InitializeComponent();
        }

        #endregion

        #region Methods


        #region Ui

        protected override void btnHelp_Click(object sender, EventArgs e)
        {
            System.Diagnostics.Process.Start("https://becagroup.sharepoint.com/KnowledgeCentre/Buildings/BIMBrilliance/Pages/Pipe%20Insulation%20Placer.aspx ");
        }



        #region Butoon Clicks




        #endregion

        #endregion

        #endregion

    }
}
