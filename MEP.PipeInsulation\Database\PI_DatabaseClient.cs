﻿using Autodesk.Revit.Creation;
using Autodesk.Revit.UI;
using Autodesk.Revit.DB;
using Microsoft.Data.SqlClient;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Security.Cryptography;
using FilterTreeControlWPF;

namespace MEP.PipeInsulation.Database
{
    public class PI_DatabaseClient : IDisposable
    {
        private SqlConnection connection;

        public PI_DatabaseClient()
        {
            connection = null;
        }

        public void Connect()
        {
            try
            {
                // Get connection string from secure configuration
                string connectionString = PI_SecureConfigManager.GetConnectionString();

                connection = new SqlConnection(connectionString);
                connection.Open();
                Console.WriteLine("Connection successful");
            }
            catch (SqlException e)
            {
                MessageBox.Show($"Error connecting to SQL Server: {e.Message}", "Connection error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                connection = null;
            }
            catch (Exception e)
            {
                MessageBox.Show($"Configuration error: {e.Message}", "Configuration error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                connection = null;
            }
        }

        public DataTable GetAllRowsFromMasterTable()
        {
            if (connection == null || connection.State != ConnectionState.Open)
            {
                MessageBox.Show("No connection established. Call Connect() first.", "Connection error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return new DataTable();  // Return an empty DataTable if connection fails
            }

            var tableName = "PI_PipeInsulationLookup_Master";
            try
            {
                // Parameterization to protect against SQL injection in dynamic table names
                using (SqlCommand command = new SqlCommand($"SELECT * FROM {tableName}", connection))
                {
                    // Adapter to fill DataTable with query results
                    using (SqlDataAdapter adapter = new SqlDataAdapter(command))
                    {
                        DataTable dataTable = new DataTable();

                        // Fill the DataTable with fetched rows
                        adapter.Fill(dataTable);

                        // Log the number of rows fetched (optional for debugging)
                        Console.WriteLine($"Fetched {dataTable.Rows.Count} rows from table '{tableName}'.");

                        return dataTable; // Return populated DataTable
                    }
                }
            }
            catch (SqlException e)
            {
                MessageBox.Show($"Error fetching rows from table '{tableName}': {e.Message}", "SQL Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return new DataTable(); // Return an empty DataTable on error
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Unexpected error: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return new DataTable(); // Return an empty DataTable on error
            }
        }

        public DataTable GetAllRowsFromCustomTable(string buid, string defaultCountry)
        {
            if (connection == null || connection.State != ConnectionState.Open)
            {
                MessageBox.Show("No connection established. Call Connect() first.", "Connection error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return new DataTable();  // Return an empty DataTable if connection fails
            }

            var customTableName = "PI_PipeInsulationLookup_Custom";
            var masterTableName = "PI_PipeInsulationLookup_Master";

            try
            {
                // Try to fetch rows from the custom table
                using (SqlCommand command = new SqlCommand($"SELECT [Country], [System], [DesignLevel], [Commercial], " +
                                                           "[Acoustic], [Material], [Location], [MinDiameter], [MaxDiameter], [Value] " +
                                                           $"FROM {customTableName} WHERE BUID = @BUID", connection))
                {
                    // Add parameter safely to prevent SQL injection
                    command.Parameters.AddWithValue("@BUID", buid);

                    using (SqlDataAdapter adapter = new SqlDataAdapter(command))
                    {
                        DataTable customDataTable = new DataTable();
                        adapter.Fill(customDataTable);

                        // If rows exist in the custom table, return them
                        if (customDataTable.Rows.Count > 0)
                        {
                            Console.WriteLine($"Fetched {customDataTable.Rows.Count} rows from table '{customTableName}'.");
                            return customDataTable;
                        }
                    }
                }

                // Fetch rows from the master table if no rows exist in the custom table
                using (SqlCommand masterCommand = new SqlCommand($"SELECT [Country], [System], [DesignLevel], [Commercial], " +
                                                                 "[Acoustic], [Material], [Location], [MinDiameter], [MaxDiameter], [Value] " +
                                                                 $"FROM {masterTableName} WHERE Country = @defaultCountry", connection))
                {
                    // Add parameters for the master table query
                    masterCommand.Parameters.AddWithValue("@defaultCountry", defaultCountry);

                    using (SqlDataAdapter masterAdapter = new SqlDataAdapter(masterCommand))
                    {
                        DataTable masterDataTable = new DataTable();
                        masterAdapter.Fill(masterDataTable);

                        Console.WriteLine($"Fetched {masterDataTable.Rows.Count} rows from table '{masterTableName}'.");

                        // If no rows are found in the master table, return an empty DataTable
                        if (masterDataTable.Rows.Count == 0)
                        {
                            Console.WriteLine($"No rows found in table '{masterTableName}' for Country='{defaultCountry}'.");
                            return new DataTable();
                        }

                        // Modify the 'Country' column in the fetched masterDataTable
                        foreach (DataRow row in masterDataTable.Rows)
                        {
                            row["Country"] = PI_SourceNames.Custom; // Update each row's Country to "Custom"
                        }

                        // Add the BUID column before performing bulk insert
                        if (!masterDataTable.Columns.Contains("BUID"))
                        {
                            masterDataTable.Columns.Add("BUID", typeof(string)); // Add the BUID column
                        }

                        foreach (DataRow row in masterDataTable.Rows)
                        {
                            row["BUID"] = buid; // Set BUID value for each row
                        }

                        // Insert rows into the custom table using SqlBulkCopy
                        using (SqlBulkCopy bulkCopy = new SqlBulkCopy(connection))
                        {
                            bulkCopy.DestinationTableName = customTableName;

                            // Map columns explicitly (if column names differ)
                            bulkCopy.ColumnMappings.Add("BUID", "BUID");
                            bulkCopy.ColumnMappings.Add("Country", "Country");
                            bulkCopy.ColumnMappings.Add("System", "System");
                            bulkCopy.ColumnMappings.Add("DesignLevel", "DesignLevel");
                            bulkCopy.ColumnMappings.Add("Commercial", "Commercial");
                            bulkCopy.ColumnMappings.Add("Acoustic", "Acoustic");
                            bulkCopy.ColumnMappings.Add("Material", "Material");
                            bulkCopy.ColumnMappings.Add("Location", "Location");
                            bulkCopy.ColumnMappings.Add("MinDiameter", "MinDiameter");
                            bulkCopy.ColumnMappings.Add("MaxDiameter", "MaxDiameter");
                            bulkCopy.ColumnMappings.Add("Value", "Value");

                            bulkCopy.WriteToServer(masterDataTable); // Perform the bulk insert
                        }

                        Console.WriteLine($"Inserted {masterDataTable.Rows.Count} rows into table '{customTableName}'.");
                        return masterDataTable; // Return the master data after adding it to the custom table
                    }
                }
            }
            catch (SqlException e)
            {
                MessageBox.Show($"Error fetching rows from tables: {e.Message}", "SQL Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return new DataTable(); // Return an empty DataTable on error
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Unexpected error: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return new DataTable(); // Return an empty DataTable on error
            }
        }

        public void UpdateCustomTable(string buid, DataTable customDataTable)
        {
            if (connection == null || connection.State != ConnectionState.Open)
            {
                MessageBox.Show("No connection established. Call Connect() first.", "Connection error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }

            try
            {
                // Delete existing rows for this BUID
                using (SqlCommand deleteCmd = new SqlCommand("DELETE FROM [dbo].[PI_PipeInsulationLookup_Custom] WHERE BUID = @BUID", connection))
                {
                    deleteCmd.Parameters.AddWithValue("@BUID", buid);
                    deleteCmd.ExecuteNonQuery();
                }

                // Bulk insert new rows for this BUID
                using (SqlBulkCopy bulkCopy = new SqlBulkCopy(connection))
                {
                    bulkCopy.DestinationTableName = "[dbo].[PI_PipeInsulationLookup_Custom]";

                    // Map columns -- MUST match your SQL table columns
                    bulkCopy.ColumnMappings.Add("BUID", "BUID");
                    bulkCopy.ColumnMappings.Add("Country", "Country");
                    bulkCopy.ColumnMappings.Add("System", "System");
                    bulkCopy.ColumnMappings.Add("DesignLevel", "DesignLevel");
                    bulkCopy.ColumnMappings.Add("Commercial", "Commercial");
                    bulkCopy.ColumnMappings.Add("Acoustic", "Acoustic");
                    bulkCopy.ColumnMappings.Add("Material", "Material");
                    bulkCopy.ColumnMappings.Add("Location", "Location");
                    bulkCopy.ColumnMappings.Add("MinDiameter", "MinDiameter");
                    bulkCopy.ColumnMappings.Add("MaxDiameter", "MaxDiameter");
                    bulkCopy.ColumnMappings.Add("Value", "Value");

                    // Perform the bulk copy
                    bulkCopy.WriteToServer(customDataTable);
                }

                MessageBox.Show("Successfully updated the custom database");
            }
            catch (SqlException e)
            {
                MessageBox.Show($"Error fetching rows from tables: {e.Message}", "SQL Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Unexpected error: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public void Close()
        {
            if (connection != null)
            {
                connection.Close();
                Console.WriteLine("Connection closed");
            }
        }

        public void Dispose()
        {
            this.Close();
        }
    }
}
