﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
//#if TargetYear2025
using Microsoft.Data.SqlClient;
//#else
//using System.Data.SqlClient;
//#endif

namespace BecaAzureSQL
{
    /// <summary>
    /// This class helps in building and configuring SqlCommand objects.
    /// </summary>
    public class SqlCommandBuilder
    {
        /// <summary>
        /// Creates a new SqlCommand with the specified SqlConnection, query, and CommandType.
        /// </summary>
        /// <param name="connection"></param>
        /// <param name="query"></param>
        /// <param name="commandType"></param>
        /// <returns></returns>
        public SqlCommand CreateCommand(SqlConnection connection, string query, CommandType commandType = CommandType.Text)
        {
            // Create a new instance of SqlCommand using the provided SqlConnection.
            SqlCommand command = connection.CreateCommand();

            // Set the command text (SQL query) and command type for the SqlCommand.
            command.CommandText = query;
            command.CommandType = commandType;

            // Return the configured SqlCommand.
            return command;
        }

        /// <summary>
        /// Adds parameters to the given SqlCommand based on the provided parameter dictionary.
        /// </summary>
        /// <param name="command"></param>
        /// <param name="parameters"></param>
        public void AddParameters(SqlCommand command, Dictionary<string, object> parameters)
        {
            if (parameters != null)
            {
                foreach (var parameter in parameters)
                {
                    command.Parameters.AddWithValue(parameter.Key, parameter.Value ?? DBNull.Value);
                }
            }
        }
    }
}
