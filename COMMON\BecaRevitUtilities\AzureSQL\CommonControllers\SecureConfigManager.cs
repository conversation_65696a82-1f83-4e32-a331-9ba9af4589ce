using System;
using System.Configuration;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace BecaRevitUtilities.AzureSQL.CommonControllers
{
    /// <summary>
    /// Common secure configuration manager for handling encrypted database connection strings
    /// This class provides a centralized way to manage encrypted configuration across multiple projects
    /// </summary>
    public static class SecureConfigManager
    {
        private static readonly string DefaultConfigFileName = "BecaRevit.config";
        private static readonly string EncryptionKey = GetCompanyEncryptionKey();

        /// <summary>
        /// Gets the database connection string from encrypted configuration
        /// </summary>
        /// <param name="configFileName">Optional custom config file name. If null, uses default.</param>
        /// <param name="connectionStringName">Connection string name in app.config for fallback</param>
        /// <returns>Decrypted connection string</returns>
        public static string GetConnectionString(string configFileName = null, string connectionStringName = null)
        {
            try
            {
                // First try to get from app.config (for development/testing)
                if (!string.IsNullOrEmpty(connectionStringName))
                {
                    var connectionString = ConfigurationManager.ConnectionStrings[connectionStringName]?.ConnectionString;
                    if (!string.IsNullOrEmpty(connectionString))
                    {
                        return connectionString;
                    }
                }

                // Try to get from encrypted config file
                string configPath = GetConfigFilePath(configFileName);
                if (File.Exists(configPath))
                {
                    string encryptedContent = File.ReadAllText(configPath);
                    return DecryptString(encryptedContent, EncryptionKey);
                }

                throw new ConfigurationErrorsException("No database configuration found. Please run configuration setup.");
            }
            catch (Exception ex)
            {
                throw new ConfigurationErrorsException($"Failed to retrieve database configuration: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Creates an encrypted configuration file with the connection string
        /// </summary>
        /// <param name="connectionString">The connection string to encrypt and store</param>
        /// <param name="configFileName">Optional custom config file name. If null, uses default.</param>
        public static void CreateEncryptedConfig(string connectionString, string configFileName = null)
        {
            try
            {
                string configPath = GetConfigFilePath(configFileName);
                string encryptedContent = EncryptString(connectionString, EncryptionKey);

                // Ensure directory exists
                Directory.CreateDirectory(Path.GetDirectoryName(configPath));

                File.WriteAllText(configPath, encryptedContent);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to create encrypted configuration: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Gets the path to the configuration file with multiple location fallbacks
        /// Priority: 1) Next to .dll (deployment), 2) Project directory (development), 3) User profile (secure)
        /// </summary>
        /// <param name="configFileName">Optional custom config file name. If null, uses default.</param>
        private static string GetConfigFilePath(string configFileName = null)
        {
            string fileName = configFileName ?? DefaultConfigFileName;

            // Priority 1: Next to the executing assembly (.dll location) - Best for deployment
            string assemblyLocation = System.Reflection.Assembly.GetExecutingAssembly().Location;
            string assemblyDirectory = Path.GetDirectoryName(assemblyLocation);
            string assemblyConfigPath = Path.Combine(assemblyDirectory, fileName);

            if (File.Exists(assemblyConfigPath))
            {
                return assemblyConfigPath;
            }

            // Priority 2: Project directory (for development) - Look for project root
            string projectConfigPath = GetProjectDirectoryConfigPath(fileName);
            if (!string.IsNullOrEmpty(projectConfigPath) && File.Exists(projectConfigPath))
            {
                return projectConfigPath;
            }

            // Priority 3: User profile directory (secure fallback)
            string userProfilePath = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile);
            string userConfigPath = Path.Combine(userProfilePath, ".beca", "revit", fileName);
            if (File.Exists(userConfigPath))
            {
                return userConfigPath;
            }

            // Return the primary location (next to .dll) for creation if none exist
            return assemblyConfigPath;
        }

        /// <summary>
        /// Attempts to find the project directory for development scenarios
        /// </summary>
        /// <param name="fileName">Configuration file name to look for</param>
        private static string GetProjectDirectoryConfigPath(string fileName)
        {
            try
            {
                string assemblyLocation = System.Reflection.Assembly.GetExecutingAssembly().Location;
                string currentDir = Path.GetDirectoryName(assemblyLocation);

                // Walk up the directory tree looking for project indicators
                while (currentDir != null && Path.GetPathRoot(currentDir) != currentDir)
                {
                    // Look for common project indicators
                    if (Directory.GetFiles(currentDir, "*.csproj").Length > 0 ||
                        Directory.GetFiles(currentDir, "*.sln").Length > 0 ||
                        Directory.Exists(Path.Combine(currentDir, "COMMON")) ||
                        Directory.Exists(Path.Combine(currentDir, "MEP.PipeInsulation")))
                    {
                        // Found project root, look for config in a secure subdirectory
                        string configDir = Path.Combine(currentDir, "Config", "Secure");
                        if (Directory.Exists(configDir))
                        {
                            return Path.Combine(configDir, fileName);
                        }
                    }
                    currentDir = Directory.GetParent(currentDir)?.FullName;
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Gets the company-wide encryption key using dynamic generation
        /// This key is generated algorithmically to avoid hardcoding sensitive values
        /// </summary>
        private static string GetCompanyEncryptionKey()
        {
            // Generate company key using algorithmic approach (inspired by ForReferenceOnly.cs)
            string companyKey = GenerateCompanyKey();

            using (var sha256 = SHA256.Create())
            {
                byte[] hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(companyKey));
                return Convert.ToBase64String(hash);
            }
        }

        /// <summary>
        /// Generates the company key algorithmically to avoid hardcoding
        /// This uses a deterministic algorithm that produces the same result every time
        /// but doesn't expose the actual key in plain text in the source code
        /// </summary>
        private static string GenerateCompanyKey()
        {
            // Enhanced keygen algorithm based on ForReferenceOnly.cs but more sophisticated
            // This generates a deterministic key that's harder to reverse engineer
            int[] seeds = { 2100, 2500, 2200, 1800, 2700, 2400, 2300, 2600, 2000, 1900, 
                           2800, 2150, 1950, 2350, 2050, 2450, 1850, 2750, 2250, 2550,
                           2650, 1750, 2850, 2950, 1650, 2175, 2375, 2575, 2775, 2975,
                           1575, 2125 };
            
            StringBuilder sb = new StringBuilder();
            
            // Use multiple transformation layers to make reverse engineering harder
            for (int i = 0; i < seeds.Length; i++)
            {
                int seed = seeds[i];
                
                // Apply multiple mathematical transformations
                int transform1 = (seed / 25) ^ 42;
                int transform2 = (transform1 + i * 7) % 126;
                int transform3 = transform2 < 32 ? transform2 + 65 : transform2;
                
                // Ensure we get printable ASCII characters
                if (transform3 > 126) transform3 = (transform3 % 95) + 32;
                if (transform3 < 32) transform3 += 32;
                
                sb.Append((char)transform3);
            }
            
            return sb.ToString();
        }

        /// <summary>
        /// Encrypts a string using AES encryption
        /// </summary>
        private static string EncryptString(string plainText, string key)
        {
            byte[] keyBytes = Convert.FromBase64String(key);

            using (var aes = Aes.Create())
            {
                aes.Key = keyBytes;
                aes.GenerateIV();

                using (var encryptor = aes.CreateEncryptor())
                using (var msEncrypt = new MemoryStream())
                {
                    // Prepend IV to the encrypted data
                    msEncrypt.Write(aes.IV, 0, aes.IV.Length);

                    using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                    using (var swEncrypt = new StreamWriter(csEncrypt))
                    {
                        swEncrypt.Write(plainText);
                    }

                    return Convert.ToBase64String(msEncrypt.ToArray());
                }
            }
        }

        /// <summary>
        /// Decrypts a string using AES decryption
        /// </summary>
        private static string DecryptString(string cipherText, string key)
        {
            byte[] keyBytes = Convert.FromBase64String(key);
            byte[] cipherBytes = Convert.FromBase64String(cipherText);

            using (var aes = Aes.Create())
            {
                aes.Key = keyBytes;

                // Extract IV from the beginning of the cipher text
                byte[] iv = new byte[aes.BlockSize / 8];
                Array.Copy(cipherBytes, 0, iv, 0, iv.Length);
                aes.IV = iv;

                using (var decryptor = aes.CreateDecryptor())
                using (var msDecrypt = new MemoryStream(cipherBytes, iv.Length, cipherBytes.Length - iv.Length))
                using (var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                using (var srDecrypt = new StreamReader(csDecrypt))
                {
                    return srDecrypt.ReadToEnd();
                }
            }
        }

        /// <summary>
        /// Validates if the current configuration is accessible
        /// </summary>
        /// <param name="configFileName">Optional custom config file name. If null, uses default.</param>
        /// <param name="connectionStringName">Connection string name in app.config for fallback</param>
        public static bool ValidateConfiguration(string configFileName = null, string connectionStringName = null)
        {
            try
            {
                string connectionString = GetConnectionString(configFileName, connectionStringName);
                return !string.IsNullOrEmpty(connectionString);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Creates a connection string builder with encrypted configuration
        /// </summary>
        /// <param name="server">Database server</param>
        /// <param name="database">Database name</param>
        /// <param name="username">Username</param>
        /// <param name="password">Password</param>
        /// <param name="configFileName">Optional custom config file name</param>
        /// <returns>True if configuration was created successfully</returns>
        public static bool CreateConfiguration(string server, string database, string username, string password, string configFileName = null)
        {
            try
            {
                var builder = new Microsoft.Data.SqlClient.SqlConnectionStringBuilder
                {
                    DataSource = server,
                    InitialCatalog = database,
                    UserID = username,
                    Password = password,
                    Encrypt = true,
                    TrustServerCertificate = false
                };

                CreateEncryptedConfig(builder.ConnectionString, configFileName);
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
