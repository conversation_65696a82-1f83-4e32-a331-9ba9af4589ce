<Project>
<!-- Initial Build Properties -->
	<PropertyGroup>
		<!--Platform we just target windows x64-->
		<LangVersion>latest</LangVersion>
		<PlatformTarget>x64</PlatformTarget>
		<Platform>x64</Platform>
		<RuntimeIdentifier>win-x64</RuntimeIdentifier>
		 <!-- Init as false, will be set true for 8.0net-windows -->
		<GenerateResourceUsePreserializedResources>false</GenerateResourceUsePreserializedResources>
		<!--To prevent warning-as-error NU1605 when switching Revit version, we can safely suppress this-->
		<NoWarn>$(NoWarn);NU1605</NoWarn> 
		<IsPublishable>False</IsPublishable>
		
		<!--Generally Needed for all builds-->
		<ImplicitUsings>true</ImplicitUsings>
		
		<!--Configuration Options-->
		<Configurations>Debug R20;Debug R21;Debug R22;Debug R23;Debug R24;Debug R25</Configurations>
		<Configurations>$(Configurations);Release R20;Release R21;Release R22;Release R23;Release R24;Release R25</Configurations>
		
		<OutputType>Library</OutputType>
	</PropertyGroup>

	
<!--Revit Version Specific Properties-->
	<!-- Revit 2020 -->
	<PropertyGroup Condition="$(Configuration.Contains('R20'))">
		<RevitVersion>2020</RevitVersion>
		<TargetFramework>net48</TargetFramework>
		<DefineConstants>$(DefineConstants);TargetYear2020</DefineConstants>
		<StartAction>Program</StartAction>
		<StartProgram>C:\Program Files\Autodesk\Revit $(RevitVersion)\Revit.exe</StartProgram>
		<StartArguments>/language ENG</StartArguments>
		<RunPostBuildEvent>OnBuildSuccess</RunPostBuildEvent>
	</PropertyGroup>
	
	<!-- Revit 2021 -->
	<PropertyGroup Condition="$(Configuration.Contains('R21'))">
		<RevitVersion>2021</RevitVersion>
		<TargetFramework>net48</TargetFramework>
		<DefineConstants>$(DefineConstants);TargetYear2021</DefineConstants>
		<StartAction>Program</StartAction>
		<StartProgram>C:\Program Files\Autodesk\Revit $(RevitVersion)\Revit.exe</StartProgram>
		<StartArguments>/language ENG</StartArguments>
		<RunPostBuildEvent>OnBuildSuccess</RunPostBuildEvent>
	</PropertyGroup>
	
	<!-- Revit 2022 -->
	<PropertyGroup Condition="$(Configuration.Contains('R22'))">
		<RevitVersion>2022</RevitVersion>
		<TargetFramework>net48</TargetFramework>
		<DefineConstants>$(DefineConstants);TargetYear2022</DefineConstants>
		<StartAction>Program</StartAction>
		<StartProgram>C:\Program Files\Autodesk\Revit $(RevitVersion)\Revit.exe</StartProgram>
		<StartArguments>/language ENG</StartArguments>
		<RunPostBuildEvent>OnBuildSuccess</RunPostBuildEvent>
	</PropertyGroup>
	
	<!-- Revit 2023 -->
	<PropertyGroup Condition="$(Configuration.Contains('R23'))">
		<RevitVersion>2023</RevitVersion>
		<TargetFramework>net48</TargetFramework>
		<DefineConstants>$(DefineConstants);TargetYear2023</DefineConstants>
		<StartAction>Program</StartAction>
		<StartProgram>C:\Program Files\Autodesk\Revit $(RevitVersion)\Revit.exe</StartProgram>
		<StartArguments>/language ENG</StartArguments>
		<RunPostBuildEvent>OnBuildSuccess</RunPostBuildEvent>
	</PropertyGroup>
	
	<!-- Revit 2024 -->
	<PropertyGroup Condition="$(Configuration.Contains('R24'))">
		<RevitVersion>2024</RevitVersion>
		<TargetFramework>net48</TargetFramework>
		<DefineConstants>$(DefineConstants);TargetYear2024</DefineConstants>
		<StartAction>Program</StartAction>
		<StartProgram>C:\Program Files\Autodesk\Revit $(RevitVersion)\Revit.exe</StartProgram>
		<StartArguments>/language ENG</StartArguments>
		<RunPostBuildEvent>OnBuildSuccess</RunPostBuildEvent>
	</PropertyGroup>
	
	<!-- Revit 2025 -->
	<PropertyGroup Condition="$(Configuration.Contains('R25'))">
		<RevitVersion>2025</RevitVersion>
		<TargetFramework>net8.0-windows</TargetFramework>
		<DefineConstants>$(DefineConstants);TargetYear2025</DefineConstants>
		<GenerateResourceUsePreserializedResources>true</GenerateResourceUsePreserializedResources>
		<StartAction>Program</StartAction>
		<StartProgram>C:\Program Files\Autodesk\Revit $(RevitVersion)\Revit.exe</StartProgram>
		<StartArguments>/language ENG</StartArguments>
		<RunPostBuildEvent>OnBuildSuccess</RunPostBuildEvent>
	</PropertyGroup>

	<!-- Revit 2026 -->
	<PropertyGroup Condition="$(Configuration.Contains('R26'))">
		<RevitVersion>2026</RevitVersion>
		<TargetFramework>net8.0-windows</TargetFramework>
		<DefineConstants>$(DefineConstants);TargetYear2026</DefineConstants>
		<GenerateResourceUsePreserializedResources>true</GenerateResourceUsePreserializedResources>
		<StartAction>Program</StartAction>
		<StartProgram>C:\Program Files\Autodesk\Revit $(RevitVersion)\Revit.exe</StartProgram>
		<StartArguments>/language ENG</StartArguments>
		<RunPostBuildEvent>OnBuildSuccess</RunPostBuildEvent>
	</PropertyGroup>
	
<!-- Debug Specific Properties -->
	<PropertyGroup Condition="$(Configuration.Contains('Debug'))">
		<Optimize>false</Optimize>
	</PropertyGroup>
	
<!-- Release Specific Properties -->
	<PropertyGroup Condition="$(Configuration.Contains('Release'))">
		<DebugType>pdbonly</DebugType>	<!--TEST THIS-->
		<DebugSymbols>false</DebugSymbols> <!--TEST THIS-->
		<Optimize>true</Optimize>
	</PropertyGroup>
	
<!-- Global Package References -->
	
	<!--.NET Core 8 Globals-->
	<ItemGroup Condition="'$(TargetFramework)' == 'net8.0-windows'">
		<PackageReference Include="System.Resources.Extensions" Version="8.0.0" /> <!-- Only needed for net8.0-windows, despite dotnet build saying that we need it for earlier versions (because we use msbuild) -->
	</ItemGroup>
	
	<!--.NET Framework 4.8 Globals -->
	<ItemGroup Condition="'$(TargetFramework)' == 'net48'">
	</ItemGroup>
	
	<!--Multi Target Globals-->
	<ItemGroup>
		<PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
	</ItemGroup>
</Project>


