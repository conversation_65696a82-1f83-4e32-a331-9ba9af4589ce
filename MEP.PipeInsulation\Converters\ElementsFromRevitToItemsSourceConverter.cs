﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Data;

namespace MEP.PipeInsulation.Converters
{
    public class ElementsFromRevitToItemsSourceConverter : IMultiValueConverter
    {
        /// <summary>
        /// values[0]: IsSelectSystemFromRevitChecked (bool)
        /// values[1]: AllFilteredElements
        /// values[2]: ManuallySelectedElements
        /// </summary>
        /// <param name="values"></param>
        /// <param name="targetType"></param>
        /// <param name="parameter"></param>
        /// <param name="culture"></param>
        /// <returns></returns>
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            //if (values.Length < 3)
            //    return null;

            //bool isSelectElementsChecked = false;
            //if (values[0] is bool b1) isSelectElementsChecked = b1;
            //if (values[2] is bool b2) isSelectElementsChecked |= b2; // combine both flags if needed

            //var allFilteredElements = values[1];

            //return isSelectElementsChecked ? allFilteredElements : null; // or new List<

            if (values.Length < 3)
                return null;

            bool isSelectSystemFromRevitChecked = values[0] as bool? == true;

            if (isSelectSystemFromRevitChecked)
                return values[2]; // ManuallySelectedElements
            else
                return values[1]; // AllFilteredElements
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
