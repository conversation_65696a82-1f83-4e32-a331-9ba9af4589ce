﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Plumbing;
using Autodesk.Revit.UI;
using BecaRevitUtilities.ElementUtilities;
using BecaTransactionsNamesManager;
using Common.UI.Forms;
using MEP.PipeInsulation.Models;
using MEP.PipeInsulation.ViewModels;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TaskDialog = Autodesk.Revit.UI.TaskDialog;

namespace MEP.PipeInsulation.CoreLogic
{
    public class InsulationPlacementLogic
    {
        Document _doc;
        Guid _guidBecaTypeMark = Guid.Parse("bbee1f29-d4dd-4b1e-bbf3-3328cc743dda");
        string _becaTypeName;
        ElementId _liningTypeID;
        ElementId _insulationTypeID;

        public InsulationPlacementLogic(Document doc)
        {
            _doc = doc;

        }

        public void RemoveAllInsulations(List<PI_Element> ductsAndFittings)
        {
            if (!TypeIdsAreOK())
                return;

            var insulationsToDelete = new List<ElementId>();
            var insulations = new FilteredElementCollector(_doc)
                .OfClass(typeof(Autodesk.Revit.DB.Plumbing.PipeInsulation))
                .Cast<Autodesk.Revit.DB.Plumbing.PipeInsulation>()
                .ToList();

            foreach (var insulation in insulations)
            {
                var matchingDuct = ductsAndFittings.FirstOrDefault(duct => duct.Element.GetElementIdValue() == insulation.GetElementIdValue());
                if (matchingDuct != null)
                {
                    insulationsToDelete.Add(insulation.Id);
                }
            }

            using (var trans = new Transaction(_doc, "Remove pipe insulations"))
            {
                trans.Start();
                try
                {
                    _doc.Delete(insulationsToDelete);
                }
                catch (Exception e)
                {
                    TaskDialog.Show("Error", e.Message);
                }

                trans.Commit();
            }

        }

        public void RemoveAllInsulationsWPF(PI_MainViewModel viewModel)
        {
            var ductsAndFittings = viewModel.AllFilteredElements;

            if (!ductsAndFittings.Any())
            {
                DialogResult result = MessageBox.Show(
                    "No Pipe elements are listed. This will process all pipe elements in the model.\n\n" +
                    "Are you sure you want to proceed? Please note that processing a large number of elements may take some time.",
                    "No Pipe Elements Selected",
                    MessageBoxButtons.OKCancel,
                    MessageBoxIcon.Warning);

                if (result == DialogResult.OK)
                {
                    ductsAndFittings = viewModel.AllPipeSystems.SelectMany(ps => ps.PI_Elements).ToObservableCollection();
                }
                else
                {
                    return;
                }
            }

            if (!TypeIdsAreOK())
                return;

            var insulationsToDelete = new List<ElementId>();
            var insulations = new FilteredElementCollector(_doc)
                .OfClass(typeof(Autodesk.Revit.DB.Plumbing.PipeInsulation))
                .Cast<Autodesk.Revit.DB.Plumbing.PipeInsulation>()
                .ToList();

            foreach (var insulation in insulations)
            {
                foreach (var pI_Element in ductsAndFittings)
                {
                    // Check if the insulation is attached to the given element
                    if (insulation.HostElementId == pI_Element.Element.Id)
                    {
                        // Delete the insulation element
                        insulationsToDelete.Add(insulation.Id);
                    }
                }
            }

            using (var trans = new Transaction(_doc, "Remove pipe insulations"))
            {
                trans.Start();
                try
                {
                    _doc.Delete(insulationsToDelete);

                    foreach (var pI_Element in ductsAndFittings)
                    {
                        pI_Element.HasInsulation = false;
                        pI_Element.RevitThickness = 0;
                    }

                    TaskDialog.Show( "Result", $"{ductsAndFittings.Count} insulations successfully removed.");
                }
                catch (Exception e)
                {
                    TaskDialog.Show("Error", e.Message);
                }

                trans.Commit();
            }

        }


        private bool TypeIdsAreOK()
        {
            //Get duct insulation type element id (the first type element?)
            var insulationType = new FilteredElementCollector(_doc).OfClass(typeof(PipeInsulationType)).ToElements();

            if (insulationType.Count() > 0)
            {
                _insulationTypeID = insulationType.ElementAt(0).Id;
                return true;
            }
            else
            {
                TaskDialog.Show("Pipe Insulation", "Pipe insulation type can't be found.");
                return false;
            }

        }

    }

    
}
